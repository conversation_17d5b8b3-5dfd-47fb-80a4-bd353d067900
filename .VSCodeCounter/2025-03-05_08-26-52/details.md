# Details

Date : 2025-03-05 08:26:52

Directory d:\\1-git\\prod\\dev-soodam

Total : 245 files,  28212 codes, 805 comments, 1461 blanks, all 30478 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.eslintrc.json](/.eslintrc.json) | JSON | 9 | 0 | 1 | 10 |
| [.prettierrc.js](/.prettierrc.js) | JavaScript | 23 | 0 | 1 | 24 |
| [README.md](/README.md) | Markdown | 1 | 0 | 0 | 1 |
| [next.config.ts](/next.config.ts) | TypeScript | 27 | 1 | 1 | 29 |
| [package-lock.json](/package-lock.json) | JSON | 12,844 | 0 | 1 | 12,845 |
| [package.json](/package.json) | JSON | 59 | 0 | 1 | 60 |
| [postcss.config.mjs](/postcss.config.mjs) | JavaScript | 8 | 1 | 1 | 10 |
| [public/file.svg](/public/file.svg) | XML | 1 | 0 | 0 | 1 |
| [public/globe.svg](/public/globe.svg) | XML | 1 | 0 | 0 | 1 |
| [public/mockServiceWorker.js](/public/mockServiceWorker.js) | JavaScript | 208 | 43 | 53 | 304 |
| [public/next.svg](/public/next.svg) | XML | 1 | 0 | 0 | 1 |
| [public/vercel.svg](/public/vercel.svg) | XML | 1 | 0 | 0 | 1 |
| [public/window.svg](/public/window.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/emptyList/EmptyCustomList.tsx](/src/components/emptyList/EmptyCustomList.tsx) | TypeScript JSX | 3 | 0 | 1 | 4 |
| [src/components/emptyList/EmptySearchList.tsx](/src/components/emptyList/EmptySearchList.tsx) | TypeScript JSX | 14 | 0 | 3 | 17 |
| [src/components/emptyList/index.ts](/src/components/emptyList/index.ts) | TypeScript | 2 | 0 | 1 | 3 |
| [src/components/forms/housing/AdvertisementRegistrationForm.tsx](/src/components/forms/housing/AdvertisementRegistrationForm.tsx) | TypeScript JSX | 1,176 | 21 | 69 | 1,266 |
| [src/components/forms/housing/RequestRegistrationForm.tsx](/src/components/forms/housing/RequestRegistrationForm.tsx) | TypeScript JSX | 1,036 | 20 | 52 | 1,108 |
| [src/components/forms/index.ts](/src/components/forms/index.ts) | TypeScript | 5 | 0 | 1 | 6 |
| [src/components/forms/login/LoginForm.tsx](/src/components/forms/login/LoginForm.tsx) | TypeScript JSX | 54 | 3 | 7 | 64 |
| [src/components/forms/login/MarketerUserLoginForm.tsx](/src/components/forms/login/MarketerUserLoginForm.tsx) | TypeScript JSX | 592 | 6 | 36 | 634 |
| [src/components/forms/login/VerificationCode.tsx](/src/components/forms/login/VerificationCode.tsx) | TypeScript JSX | 149 | 2 | 19 | 170 |
| [src/components/housing/HousingCard.tsx](/src/components/housing/HousingCard.tsx) | TypeScript JSX | 118 | 14 | 11 | 143 |
| [src/components/housing/HousingFilterControls.tsx](/src/components/housing/HousingFilterControls.tsx) | TypeScript JSX | 111 | 10 | 24 | 145 |
| [src/components/housing/index.ts](/src/components/housing/index.ts) | TypeScript | 1 | 0 | 1 | 2 |
| [src/components/layouts/ClientLayout.tsx](/src/components/layouts/ClientLayout.tsx) | TypeScript JSX | 75 | 2 | 10 | 87 |
| [src/components/layouts/index.ts](/src/components/layouts/index.ts) | TypeScript | 1 | 0 | 1 | 2 |
| [src/components/map/LocationMap.tsx](/src/components/map/LocationMap.tsx) | TypeScript JSX | 32 | 0 | 4 | 36 |
| [src/components/map/Map.tsx](/src/components/map/Map.tsx) | TypeScript JSX | 1,084 | 108 | 111 | 1,303 |
| [src/components/map/MapLocationPicker.tsx](/src/components/map/MapLocationPicker.tsx) | TypeScript JSX | 539 | 14 | 70 | 623 |
| [src/components/modals/FirstLoginModal.tsx](/src/components/modals/FirstLoginModal.tsx) | TypeScript JSX | 91 | 0 | 6 | 97 |
| [src/components/modals/MemberUserLoginsModal.tsx](/src/components/modals/MemberUserLoginsModal.tsx) | TypeScript JSX | 64 | 0 | 4 | 68 |
| [src/components/modals/SearchModal.tsx](/src/components/modals/SearchModal.tsx) | TypeScript JSX | 147 | 13 | 10 | 170 |
| [src/components/modals/index.ts](/src/components/modals/index.ts) | TypeScript | 3 | 0 | 1 | 4 |
| [src/components/shared/BottomNavigation.tsx](/src/components/shared/BottomNavigation.tsx) | TypeScript JSX | 77 | 0 | 6 | 83 |
| [src/components/shared/DataStateDisplay.tsx](/src/components/shared/DataStateDisplay.tsx) | TypeScript JSX | 49 | 2 | 7 | 58 |
| [src/components/shared/FilterControlNavBar.tsx](/src/components/shared/FilterControlNavBar.tsx) | TypeScript JSX | 156 | 9 | 21 | 186 |
| [src/components/shared/FilterControlsHeader.tsx](/src/components/shared/FilterControlsHeader.tsx) | TypeScript JSX | 59 | 1 | 5 | 65 |
| [src/components/shared/Footer.tsx](/src/components/shared/Footer.tsx) | TypeScript JSX | 7 | 0 | 2 | 9 |
| [src/components/shared/HandleResponse.tsx](/src/components/shared/HandleResponse.tsx) | TypeScript JSX | 29 | 15 | 14 | 58 |
| [src/components/shared/Header.tsx](/src/components/shared/Header.tsx) | TypeScript JSX | 77 | 0 | 8 | 85 |
| [src/components/shared/MapMode.tsx](/src/components/shared/MapMode.tsx) | TypeScript JSX | 24 | 0 | 3 | 27 |
| [src/components/shared/Sidebar.tsx](/src/components/shared/Sidebar.tsx) | TypeScript JSX | 211 | 1 | 10 | 222 |
| [src/components/shared/index.ts](/src/components/shared/index.ts) | TypeScript | 8 | 0 | 0 | 8 |
| [src/components/skeleton/HousingSkeleton.tsx](/src/components/skeleton/HousingSkeleton.tsx) | TypeScript JSX | 26 | 0 | 2 | 28 |
| [src/components/skeleton/NavbarSkeleton.tsx](/src/components/skeleton/NavbarSkeleton.tsx) | TypeScript JSX | 11 | 0 | 2 | 13 |
| [src/components/skeleton/index.ts](/src/components/skeleton/index.ts) | TypeScript | 2 | 0 | 1 | 3 |
| [src/components/sliders/HousingSliders.tsx](/src/components/sliders/HousingSliders.tsx) | TypeScript JSX | 73 | 5 | 5 | 83 |
| [src/components/sliders/index.ts](/src/components/sliders/index.ts) | TypeScript | 1 | 0 | 1 | 2 |
| [src/components/ui/Alert.tsx](/src/components/ui/Alert.tsx) | TypeScript JSX | 53 | 4 | 7 | 64 |
| [src/components/ui/Buttons.tsx](/src/components/ui/Buttons.tsx) | TypeScript JSX | 31 | 2 | 8 | 41 |
| [src/components/ui/Checkbox.tsx](/src/components/ui/Checkbox.tsx) | TypeScript JSX | 45 | 0 | 7 | 52 |
| [src/components/ui/DisplayError.tsx](/src/components/ui/DisplayError.tsx) | TypeScript JSX | 70 | 0 | 8 | 78 |
| [src/components/ui/Modal.tsx](/src/components/ui/Modal.tsx) | TypeScript JSX | 143 | 4 | 18 | 165 |
| [src/components/ui/SelectBox.tsx](/src/components/ui/SelectBox.tsx) | TypeScript JSX | 90 | 7 | 12 | 109 |
| [src/components/ui/Skeleton.tsx](/src/components/ui/Skeleton.tsx) | TypeScript JSX | 63 | 4 | 13 | 80 |
| [src/components/ui/TextField.tsx](/src/components/ui/TextField.tsx) | TypeScript JSX | 73 | 6 | 11 | 90 |
| [src/components/ui/TextFiledPrice.tsx](/src/components/ui/TextFiledPrice.tsx) | TypeScript JSX | 90 | 6 | 10 | 106 |
| [src/components/ui/index.ts](/src/components/ui/index.ts) | TypeScript | 13 | 1 | 5 | 19 |
| [src/components/ui/loading/FullInlineLoading.tsx](/src/components/ui/loading/FullInlineLoading.tsx) | TypeScript JSX | 68 | 0 | 3 | 71 |
| [src/components/ui/loading/FullScreenLoading.tsx](/src/components/ui/loading/FullScreenLoading.tsx) | TypeScript JSX | 8 | 0 | 2 | 10 |
| [src/components/ui/loading/InlineLoading.tsx](/src/components/ui/loading/InlineLoading.tsx) | TypeScript JSX | 68 | 1 | 0 | 69 |
| [src/components/ui/loading/LoadingScreen.tsx](/src/components/ui/loading/LoadingScreen.tsx) | TypeScript JSX | 16 | 1 | 3 | 20 |
| [src/components/ui/loading/PageTransitionLoading.tsx](/src/components/ui/loading/PageTransitionLoading.tsx) | TypeScript JSX | 0 | 32 | 7 | 39 |
| [src/components/user/LogoutButton.tsx](/src/components/user/LogoutButton.tsx) | TypeScript JSX | 29 | 2 | 4 | 35 |
| [src/components/user/ProtectedRouteWrapper.tsx](/src/components/user/ProtectedRouteWrapper.tsx) | TypeScript JSX | 0 | 24 | 10 | 34 |
| [src/components/user/SubscriptionCard.tsx](/src/components/user/SubscriptionCard.tsx) | TypeScript JSX | 23 | 3 | 4 | 30 |
| [src/components/user/index.ts](/src/components/user/index.ts) | TypeScript | 2 | 0 | 1 | 3 |
| [src/hooks/index.ts](/src/hooks/index.ts) | TypeScript | 4 | 2 | 1 | 7 |
| [src/hooks/use-filter.ts](/src/hooks/use-filter.ts) | TypeScript | 45 | 0 | 8 | 53 |
| [src/hooks/useChangeRoute.ts](/src/hooks/useChangeRoute.ts) | TypeScript | 14 | 0 | 7 | 21 |
| [src/hooks/useDebounce.ts](/src/hooks/useDebounce.ts) | TypeScript | 12 | 0 | 6 | 18 |
| [src/hooks/useDisclosure.ts](/src/hooks/useDisclosure.ts) | TypeScript | 14 | 1 | 6 | 21 |
| [src/hooks/useRedux.ts](/src/hooks/useRedux.ts) | TypeScript | 4 | 0 | 2 | 6 |
| [src/icons/index.ts](/src/icons/index.ts) | TypeScript | 183 | 1 | 4 | 188 |
| [src/icons/svgs/1017280091582962144 (1) 1.svg](/src/icons/svgs/1017280091582962144%20(1)%201.svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/13724094091679983182 1.svg](/src/icons/svgs/13724094091679983182%201.svg) | XML | 35 | 0 | 1 | 36 |
| [src/icons/svgs/18237869081579156621 1.svg](/src/icons/svgs/18237869081579156621%201.svg) | XML | 9 | 0 | 1 | 10 |
| [src/icons/svgs/20016826761595759881 1.svg](/src/icons/svgs/20016826761595759881%201.svg) | XML | 18 | 0 | 1 | 19 |
| [src/icons/svgs/3d-cube-scan.svg](/src/icons/svgs/3d-cube-scan.svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/3d-rotate.svg](/src/icons/svgs/3d-rotate.svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/3dcube2.svg](/src/icons/svgs/3dcube2.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/Bed.svg](/src/icons/svgs/Bed.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/Eliipse.svg](/src/icons/svgs/Eliipse.svg) | XML | 15 | 0 | 1 | 16 |
| [src/icons/svgs/Frame.svg](/src/icons/svgs/Frame.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/Group 1000011001.svg](/src/icons/svgs/Group%201000011001.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/Group 1000011157.svg](/src/icons/svgs/Group%201000011157.svg) | XML | 4 | 0 | 1 | 5 |
| [src/icons/svgs/Group 7.svg](/src/icons/svgs/Group%207.svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/Group.svg](/src/icons/svgs/Group.svg) | XML | 36 | 0 | 1 | 37 |
| [src/icons/svgs/Send.svg](/src/icons/svgs/Send.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/Upload icon.svg](/src/icons/svgs/Upload%20icon.svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/Vector (1).svg](/src/icons/svgs/Vector%20(1).svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/Vector (2).svg](/src/icons/svgs/Vector%20(2).svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/Vector 395.svg](/src/icons/svgs/Vector%20395.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/Vector.svg](/src/icons/svgs/Vector.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/add-circle.svg](/src/icons/svgs/add-circle.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/archive-tick.svg](/src/icons/svgs/archive-tick.svg) | XML | 4 | 0 | 1 | 5 |
| [src/icons/svgs/archive-tick2.svg](/src/icons/svgs/archive-tick2.svg) | XML | 4 | 0 | 1 | 5 |
| [src/icons/svgs/arrow-left.svg](/src/icons/svgs/arrow-left.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/battery-full.svg](/src/icons/svgs/battery-full.svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/buliding (1).svg](/src/icons/svgs/buliding%20(1).svg) | XML | 10 | 0 | 1 | 11 |
| [src/icons/svgs/buliding.svg](/src/icons/svgs/buliding.svg) | XML | 10 | 0 | 1 | 11 |
| [src/icons/svgs/button.svg](/src/icons/svgs/button.svg) | XML | 17 | 0 | 1 | 18 |
| [src/icons/svgs/calendar-search.svg](/src/icons/svgs/calendar-search.svg) | XML | 11 | 0 | 1 | 12 |
| [src/icons/svgs/calendar-tick.svg](/src/icons/svgs/calendar-tick.svg) | XML | 11 | 0 | 1 | 12 |
| [src/icons/svgs/calendar.svg](/src/icons/svgs/calendar.svg) | XML | 12 | 0 | 1 | 13 |
| [src/icons/svgs/call.svg](/src/icons/svgs/call.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/camera-small.svg](/src/icons/svgs/camera-small.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/camera.svg](/src/icons/svgs/camera.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/camera2.svg](/src/icons/svgs/camera2.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/card-tick.svg](/src/icons/svgs/card-tick.svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/chage.svg](/src/icons/svgs/chage.svg) | XML | 8 | 0 | 1 | 9 |
| [src/icons/svgs/change.svg](/src/icons/svgs/change.svg) | XML | 8 | 0 | 1 | 9 |
| [src/icons/svgs/check.svg](/src/icons/svgs/check.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/clock2.svg](/src/icons/svgs/clock2.svg) | XML | 4 | 0 | 1 | 5 |
| [src/icons/svgs/close-circle.svg](/src/icons/svgs/close-circle.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/eye.svg](/src/icons/svgs/eye.svg) | XML | 4 | 0 | 1 | 5 |
| [src/icons/svgs/gear-settings.svg](/src/icons/svgs/gear-settings.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/gps.svg](/src/icons/svgs/gps.svg) | XML | 8 | 0 | 1 | 9 |
| [src/icons/svgs/grid-2.svg](/src/icons/svgs/grid-2.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/headphone.svg](/src/icons/svgs/headphone.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/heart 1.svg](/src/icons/svgs/heart%201.svg) | XML | 10 | 0 | 1 | 11 |
| [src/icons/svgs/home-2.svg](/src/icons/svgs/home-2.svg) | XML | 4 | 0 | 1 | 5 |
| [src/icons/svgs/home.svg](/src/icons/svgs/home.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/hospital.svg](/src/icons/svgs/hospital.svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/house.svg](/src/icons/svgs/house.svg) | XML | 8 | 0 | 1 | 9 |
| [src/icons/svgs/info-circle (1).svg](/src/icons/svgs/info-circle%20(1).svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/info-circle.svg](/src/icons/svgs/info-circle.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/location.svg](/src/icons/svgs/location.svg) | XML | 4 | 0 | 1 | 5 |
| [src/icons/svgs/location2.svg](/src/icons/svgs/location2.svg) | XML | 4 | 0 | 1 | 5 |
| [src/icons/svgs/location22.svg](/src/icons/svgs/location22.svg) | XML | 4 | 0 | 1 | 5 |
| [src/icons/svgs/locationred.svg](/src/icons/svgs/locationred.svg) | XML | 4 | 0 | 1 | 5 |
| [src/icons/svgs/logout.svg](/src/icons/svgs/logout.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/map (1).svg](/src/icons/svgs/map%20(1).svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/map copy.svg](/src/icons/svgs/map%20copy.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/map.svg](/src/icons/svgs/map.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/menu.svg](/src/icons/svgs/menu.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/mirror (1).svg](/src/icons/svgs/mirror%20(1).svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/more.svg](/src/icons/svgs/more.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/note-favorite.svg](/src/icons/svgs/note-favorite.svg) | XML | 9 | 0 | 1 | 10 |
| [src/icons/svgs/note-text.svg](/src/icons/svgs/note-text.svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/notification-bing.svg](/src/icons/svgs/notification-bing.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/one-finger-swipe-left (1).svg](/src/icons/svgs/one-finger-swipe-left%20(1).svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/one-finger-swipe-left (2).svg](/src/icons/svgs/one-finger-swipe-left%20(2).svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/one-finger-swipe-left copy.svg](/src/icons/svgs/one-finger-swipe-left%20copy.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/one-finger-swipe-left.svg](/src/icons/svgs/one-finger-swipe-left.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/people.svg](/src/icons/svgs/people.svg) | XML | 8 | 0 | 1 | 9 |
| [src/icons/svgs/profile-add.svg](/src/icons/svgs/profile-add.svg) | XML | 6 | 0 | 1 | 7 |
| [src/icons/svgs/profile-no-tick.svg](/src/icons/svgs/profile-no-tick.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/profile-tick-green.svg](/src/icons/svgs/profile-tick-green.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/profile-tick.svg](/src/icons/svgs/profile-tick.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/repeat.svg](/src/icons/svgs/repeat.svg) | XML | 6 | 0 | 1 | 7 |
| [src/icons/svgs/search-normal.svg](/src/icons/svgs/search-normal.svg) | XML | 4 | 0 | 1 | 5 |
| [src/icons/svgs/search.svg](/src/icons/svgs/search.svg) | XML | 3 | 0 | 1 | 4 |
| [src/icons/svgs/sms-edit.svg](/src/icons/svgs/sms-edit.svg) | XML | 6 | 0 | 1 | 7 |
| [src/icons/svgs/sms-edit1.svg](/src/icons/svgs/sms-edit1.svg) | XML | 6 | 0 | 1 | 7 |
| [src/icons/svgs/task-square.svg](/src/icons/svgs/task-square.svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/ticket-discount.svg](/src/icons/svgs/ticket-discount.svg) | XML | 6 | 0 | 1 | 7 |
| [src/icons/svgs/ticket-star.svg](/src/icons/svgs/ticket-star.svg) | XML | 6 | 0 | 1 | 7 |
| [src/icons/svgs/ticket-star2.svg](/src/icons/svgs/ticket-star2.svg) | XML | 6 | 0 | 1 | 7 |
| [src/icons/svgs/user-edit.svg](/src/icons/svgs/user-edit.svg) | XML | 6 | 0 | 1 | 7 |
| [src/icons/svgs/user-edit2.svg](/src/icons/svgs/user-edit2.svg) | XML | 6 | 0 | 1 | 7 |
| [src/icons/svgs/video.svg](/src/icons/svgs/video.svg) | XML | 5 | 0 | 1 | 6 |
| [src/icons/svgs/wallet.svg](/src/icons/svgs/wallet.svg) | XML | 7 | 0 | 1 | 8 |
| [src/icons/svgs/warning-22.svg](/src/icons/svgs/warning-22.svg) | XML | 5 | 0 | 1 | 6 |
| [src/mocks/browser.ts](/src/mocks/browser.ts) | TypeScript | 3 | 0 | 2 | 5 |
| [src/mocks/handlers.ts](/src/mocks/handlers.ts) | TypeScript | 2,146 | 144 | 118 | 2,408 |
| [src/mocks/server.ts](/src/mocks/server.ts) | TypeScript | 3 | 0 | 2 | 5 |
| [src/pages/\_app.tsx](/src/pages/_app.tsx) | TypeScript JSX | 45 | 4 | 8 | 57 |
| [src/pages/\_document.tsx](/src/pages/_document.tsx) | TypeScript JSX | 90 | 1 | 3 | 94 |
| [src/pages/api/hello.ts](/src/pages/api/hello.ts) | TypeScript | 10 | 1 | 3 | 14 |
| [src/pages/authentication/login/index.tsx](/src/pages/authentication/login/index.tsx) | TypeScript JSX | 105 | 12 | 8 | 125 |
| [src/pages/contacts/index.tsx](/src/pages/contacts/index.tsx) | TypeScript JSX | 132 | 3 | 9 | 144 |
| [src/pages/filterControls/index.tsx](/src/pages/filterControls/index.tsx) | TypeScript JSX | 729 | 17 | 49 | 795 |
| [src/pages/housing/\[adCode\].tsx](/src/pages/housing/%5BadCode%5D.tsx) | TypeScript JSX | 436 | 21 | 38 | 495 |
| [src/pages/housing/ad/index.tsx](/src/pages/housing/ad/index.tsx) | TypeScript JSX | 77 | 25 | 5 | 107 |
| [src/pages/housing/ad/new/index.tsx](/src/pages/housing/ad/new/index.tsx) | TypeScript JSX | 87 | 3 | 3 | 93 |
| [src/pages/housing/my-ads/index.tsx](/src/pages/housing/my-ads/index.tsx) | TypeScript JSX | 48 | 1 | 3 | 52 |
| [src/pages/housing/unauthorize.tsx](/src/pages/housing/unauthorize.tsx) | TypeScript JSX | 50 | 1 | 6 | 57 |
| [src/pages/index.tsx](/src/pages/index.tsx) | TypeScript JSX | 116 | 2 | 8 | 126 |
| [src/pages/lists/index.tsx](/src/pages/lists/index.tsx) | TypeScript JSX | 113 | 2 | 9 | 124 |
| [src/pages/marketer/index.tsx](/src/pages/marketer/index.tsx) | TypeScript JSX | 79 | 2 | 8 | 89 |
| [src/pages/marketer/register/index.tsx](/src/pages/marketer/register/index.tsx) | TypeScript JSX | 21 | 2 | 5 | 28 |
| [src/pages/memberShip/index.tsx](/src/pages/memberShip/index.tsx) | TypeScript JSX | 17 | 2 | 3 | 22 |
| [src/pages/my-ads-viewStatistics/index.tsx](/src/pages/my-ads-viewStatistics/index.tsx) | TypeScript JSX | 31 | 1 | 4 | 36 |
| [src/pages/my-payments/index.tsx](/src/pages/my-payments/index.tsx) | TypeScript JSX | 31 | 1 | 4 | 36 |
| [src/pages/myCity/index.tsx](/src/pages/myCity/index.tsx) | TypeScript JSX | 19 | 2 | 3 | 24 |
| [src/pages/news/index.tsx](/src/pages/news/index.tsx) | TypeScript JSX | 56 | 0 | 4 | 60 |
| [src/pages/requests/index.tsx](/src/pages/requests/index.tsx) | TypeScript JSX | 46 | 3 | 3 | 52 |
| [src/pages/requests/new/index.tsx](/src/pages/requests/new/index.tsx) | TypeScript JSX | 16 | 1 | 3 | 20 |
| [src/pages/soodam/account/index.tsx](/src/pages/soodam/account/index.tsx) | TypeScript JSX | 357 | 4 | 14 | 375 |
| [src/pages/soodam/index.tsx](/src/pages/soodam/index.tsx) | TypeScript JSX | 124 | 4 | 9 | 137 |
| [src/pages/subscription/index.tsx](/src/pages/subscription/index.tsx) | TypeScript JSX | 167 | 27 | 18 | 212 |
| [src/pages/visitStatistics/index.tsx](/src/pages/visitStatistics/index.tsx) | TypeScript JSX | 242 | 11 | 17 | 270 |
| [src/services/addressesApiSlice.ts](/src/services/addressesApiSlice.ts) | TypeScript | 18 | 1 | 3 | 22 |
| [src/services/allHousing/apiSlice.ts](/src/services/allHousing/apiSlice.ts) | TypeScript | 49 | 0 | 6 | 55 |
| [src/services/allHousing/types.ts](/src/services/allHousing/types.ts) | TypeScript | 3 | 0 | 1 | 4 |
| [src/services/auth/apiSlice.ts](/src/services/auth/apiSlice.ts) | TypeScript | 60 | 61 | 8 | 129 |
| [src/services/auth/types.ts](/src/services/auth/types.ts) | TypeScript | 17 | 0 | 3 | 20 |
| [src/services/baseApi.ts](/src/services/baseApi.ts) | TypeScript | 21 | 0 | 4 | 25 |
| [src/services/category/apiSlice.ts](/src/services/category/apiSlice.ts) | TypeScript | 35 | 0 | 5 | 40 |
| [src/services/category/type.ts](/src/services/category/type.ts) | TypeScript | 3 | 0 | 1 | 4 |
| [src/services/feature/apiSlice.ts](/src/services/feature/apiSlice.ts) | TypeScript | 84 | 0 | 8 | 92 |
| [src/services/feature/type.ts](/src/services/feature/type.ts) | TypeScript | 0 | 0 | 1 | 1 |
| [src/services/index.ts](/src/services/index.ts) | TypeScript | 6 | 0 | 1 | 7 |
| [src/services/mapService.ts](/src/services/mapService.ts) | TypeScript | 13 | 0 | 3 | 16 |
| [src/services/news/apiSlice.ts](/src/services/news/apiSlice.ts) | TypeScript | 27 | 0 | 4 | 31 |
| [src/services/news/type.ts](/src/services/news/type.ts) | TypeScript | 0 | 0 | 1 | 1 |
| [src/services/user/apiSlice.ts](/src/services/user/apiSlice.ts) | TypeScript | 54 | 4 | 6 | 64 |
| [src/services/user/type.ts](/src/services/user/type.ts) | TypeScript | 6 | 0 | 0 | 6 |
| [src/store/index.ts](/src/store/index.ts) | TypeScript | 34 | 4 | 6 | 44 |
| [src/store/slices/alert.slice.ts](/src/store/slices/alert.slice.ts) | TypeScript | 36 | 1 | 8 | 45 |
| [src/store/slices/auth.slice.ts](/src/store/slices/auth.slice.ts) | TypeScript | 74 | 0 | 8 | 82 |
| [src/store/slices/loginModal.slice.ts](/src/store/slices/loginModal.slice.ts) | TypeScript | 23 | 0 | 5 | 28 |
| [src/store/slices/mapMode.slice.ts](/src/store/slices/mapMode.slice.ts) | TypeScript | 18 | 0 | 5 | 23 |
| [src/store/slices/savedHouses.slice.ts](/src/store/slices/savedHouses.slice.ts) | TypeScript | 38 | 3 | 10 | 51 |
| [src/store/slices/statesData.slice.ts](/src/store/slices/statesData.slice.ts) | TypeScript | 109 | 0 | 5 | 114 |
| [src/styles/abstracts/base.css](/src/styles/abstracts/base.css) | CSS | 21 | 0 | 1 | 22 |
| [src/styles/abstracts/global.css](/src/styles/abstracts/global.css) | CSS | 12 | 0 | 1 | 13 |
| [src/styles/abstracts/index.css](/src/styles/abstracts/index.css) | CSS | 2 | 0 | 1 | 3 |
| [src/styles/browser-styles.css](/src/styles/browser-styles.css) | CSS | 31 | 9 | 9 | 49 |
| [src/styles/components.css](/src/styles/components.css) | CSS | 32 | 6 | 9 | 47 |
| [src/styles/fonts/estedad.css](/src/styles/fonts/estedad.css) | CSS | 45 | 0 | 4 | 49 |
| [src/styles/fonts/iranyekan.css](/src/styles/fonts/iranyekan.css) | CSS | 51 | 0 | 1 | 52 |
| [src/styles/main.css](/src/styles/main.css) | CSS | 90 | 1 | 19 | 110 |
| [src/styles/swiper.css](/src/styles/swiper.css) | CSS | 36 | 0 | 2 | 38 |
| [src/types/Category.type.ts](/src/types/Category.type.ts) | TypeScript | 9 | 0 | 1 | 10 |
| [src/types/Feature.type.ts](/src/types/Feature.type.ts) | TypeScript | 10 | 0 | 1 | 11 |
| [src/types/Housing.type.ts](/src/types/Housing.type.ts) | TypeScript | 39 | 0 | 1 | 40 |
| [src/types/News.type.ts](/src/types/News.type.ts) | TypeScript | 11 | 0 | 1 | 12 |
| [src/types/QueryParams.type.ts](/src/types/QueryParams.type.ts) | TypeScript | 3 | 0 | 1 | 4 |
| [src/types/Request.type.ts](/src/types/Request.type.ts) | TypeScript | 20 | 0 | 1 | 21 |
| [src/types/ServiceResponse.type.ts](/src/types/ServiceResponse.type.ts) | TypeScript | 5 | 0 | 1 | 6 |
| [src/types/SubscriptionPlan.type.ts](/src/types/SubscriptionPlan.type.ts) | TypeScript | 8 | 0 | 1 | 9 |
| [src/types/User.type.ts](/src/types/User.type.ts) | TypeScript | 15 | 0 | 1 | 16 |
| [src/types/forms.type.ts](/src/types/forms.type.ts) | TypeScript | 126 | 0 | 19 | 145 |
| [src/types/index.ts](/src/types/index.ts) | TypeScript | 10 | 0 | 2 | 12 |
| [src/utils/constants.ts](/src/utils/constants.ts) | TypeScript | 50 | 0 | 6 | 56 |
| [src/utils/generateQueryParams.ts](/src/utils/generateQueryParams.ts) | TypeScript | 9 | 0 | 4 | 13 |
| [src/utils/getErrorMessage.ts](/src/utils/getErrorMessage.ts) | TypeScript | 16 | 0 | 4 | 20 |
| [src/utils/index.ts](/src/utils/index.ts) | TypeScript | 5 | 0 | 1 | 6 |
| [src/utils/stringFormatting.ts](/src/utils/stringFormatting.ts) | TypeScript | 60 | 5 | 8 | 73 |
| [src/utils/validation.ts](/src/utils/validation.ts) | TypeScript | 272 | 20 | 42 | 334 |
| [tailwind.config.ts](/tailwind.config.ts) | TypeScript | 37 | 0 | 2 | 39 |
| [tsconfig.json](/tsconfig.json) | JSON with Comments | 24 | 1 | 1 | 26 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)