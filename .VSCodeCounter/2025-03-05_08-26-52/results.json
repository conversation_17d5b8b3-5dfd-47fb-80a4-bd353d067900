{"file:///d%3A/1-git/prod/dev-soodam/next.config.ts": {"language": "TypeScript", "code": 27, "comment": 1, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/.eslintrc.json": {"language": "JSON", "code": 9, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/.prettierrc.js": {"language": "JavaScript", "code": 23, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/README.md": {"language": "<PERSON><PERSON>", "code": 1, "comment": 0, "blank": 0}, "file:///d%3A/1-git/prod/dev-soodam/tailwind.config.ts": {"language": "TypeScript", "code": 37, "comment": 0, "blank": 2}, "file:///d%3A/1-git/prod/dev-soodam/postcss.config.mjs": {"language": "JavaScript", "code": 8, "comment": 1, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/tsconfig.json": {"language": "JSON with Comments", "code": 24, "comment": 1, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/package.json": {"language": "JSON", "code": 59, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/public/next.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///d%3A/1-git/prod/dev-soodam/public/vercel.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///d%3A/1-git/prod/dev-soodam/public/window.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///d%3A/1-git/prod/dev-soodam/src/utils/validation.ts": {"language": "TypeScript", "code": 272, "comment": 20, "blank": 42}, "file:///d%3A/1-git/prod/dev-soodam/public/mockServiceWorker.js": {"language": "JavaScript", "code": 208, "comment": 43, "blank": 53}, "file:///d%3A/1-git/prod/dev-soodam/src/utils/stringFormatting.ts": {"language": "TypeScript", "code": 60, "comment": 5, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/utils/index.ts": {"language": "TypeScript", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/utils/getErrorMessage.ts": {"language": "TypeScript", "code": 16, "comment": 0, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/utils/generateQueryParams.ts": {"language": "TypeScript", "code": 9, "comment": 0, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/utils/constants.ts": {"language": "TypeScript", "code": 50, "comment": 0, "blank": 6}, "file:///d%3A/1-git/prod/dev-soodam/src/types/Category.type.ts": {"language": "TypeScript", "code": 9, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/styles/swiper.css": {"language": "CSS", "code": 36, "comment": 0, "blank": 2}, "file:///d%3A/1-git/prod/dev-soodam/src/types/User.type.ts": {"language": "TypeScript", "code": 15, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/styles/main.css": {"language": "CSS", "code": 90, "comment": 1, "blank": 19}, "file:///d%3A/1-git/prod/dev-soodam/src/types/SubscriptionPlan.type.ts": {"language": "TypeScript", "code": 8, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/types/ServiceResponse.type.ts": {"language": "TypeScript", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/types/Request.type.ts": {"language": "TypeScript", "code": 20, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/types/QueryParams.type.ts": {"language": "TypeScript", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/types/News.type.ts": {"language": "TypeScript", "code": 11, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/types/Housing.type.ts": {"language": "TypeScript", "code": 39, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/types/index.ts": {"language": "TypeScript", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/1-git/prod/dev-soodam/src/types/forms.type.ts": {"language": "TypeScript", "code": 126, "comment": 0, "blank": 19}, "file:///d%3A/1-git/prod/dev-soodam/src/types/Feature.type.ts": {"language": "TypeScript", "code": 10, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/styles/components.css": {"language": "CSS", "code": 32, "comment": 6, "blank": 9}, "file:///d%3A/1-git/prod/dev-soodam/src/styles/browser-styles.css": {"language": "CSS", "code": 31, "comment": 9, "blank": 9}, "file:///d%3A/1-git/prod/dev-soodam/src/styles/fonts/iranyekan.css": {"language": "CSS", "code": 51, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/styles/fonts/estedad.css": {"language": "CSS", "code": 45, "comment": 0, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/styles/abstracts/index.css": {"language": "CSS", "code": 2, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/styles/abstracts/global.css": {"language": "CSS", "code": 12, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/styles/abstracts/base.css": {"language": "CSS", "code": 21, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/services/user/type.ts": {"language": "TypeScript", "code": 6, "comment": 0, "blank": 0}, "file:///d%3A/1-git/prod/dev-soodam/src/services/user/apiSlice.ts": {"language": "TypeScript", "code": 54, "comment": 4, "blank": 6}, "file:///d%3A/1-git/prod/dev-soodam/src/store/slices/statesData.slice.ts": {"language": "TypeScript", "code": 109, "comment": 0, "blank": 5}, "file:///d%3A/1-git/prod/dev-soodam/src/store/slices/alert.slice.ts": {"language": "TypeScript", "code": 36, "comment": 1, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/store/slices/savedHouses.slice.ts": {"language": "TypeScript", "code": 38, "comment": 3, "blank": 10}, "file:///d%3A/1-git/prod/dev-soodam/src/store/slices/mapMode.slice.ts": {"language": "TypeScript", "code": 18, "comment": 0, "blank": 5}, "file:///d%3A/1-git/prod/dev-soodam/src/store/slices/loginModal.slice.ts": {"language": "TypeScript", "code": 23, "comment": 0, "blank": 5}, "file:///d%3A/1-git/prod/dev-soodam/src/store/slices/auth.slice.ts": {"language": "TypeScript", "code": 74, "comment": 0, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/services/mapService.ts": {"language": "TypeScript", "code": 13, "comment": 0, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/services/index.ts": {"language": "TypeScript", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/services/news/apiSlice.ts": {"language": "TypeScript", "code": 27, "comment": 0, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/_document.tsx": {"language": "TypeScript JSX", "code": 90, "comment": 1, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/services/news/type.ts": {"language": "TypeScript", "code": 0, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/_app.tsx": {"language": "TypeScript JSX", "code": 45, "comment": 4, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/api/hello.ts": {"language": "TypeScript", "code": 10, "comment": 1, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/services/category/type.ts": {"language": "TypeScript", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/services/category/apiSlice.ts": {"language": "TypeScript", "code": 35, "comment": 0, "blank": 5}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/subscription/index.tsx": {"language": "TypeScript JSX", "code": 167, "comment": 27, "blank": 18}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/visitStatistics/index.tsx": {"language": "TypeScript JSX", "code": 242, "comment": 11, "blank": 17}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/soodam/index.tsx": {"language": "TypeScript JSX", "code": 124, "comment": 4, "blank": 9}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/news/index.tsx": {"language": "TypeScript JSX", "code": 56, "comment": 0, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/requests/index.tsx": {"language": "TypeScript JSX", "code": 46, "comment": 3, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/soodam/account/index.tsx": {"language": "TypeScript JSX", "code": 357, "comment": 4, "blank": 14}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/requests/new/index.tsx": {"language": "TypeScript JSX", "code": 16, "comment": 1, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/my-payments/index.tsx": {"language": "TypeScript JSX", "code": 31, "comment": 1, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/myCity/index.tsx": {"language": "TypeScript JSX", "code": 19, "comment": 2, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/my-ads-viewStatistics/index.tsx": {"language": "TypeScript JSX", "code": 31, "comment": 1, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/lists/index.tsx": {"language": "TypeScript JSX", "code": 113, "comment": 2, "blank": 9}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/memberShip/index.tsx": {"language": "TypeScript JSX", "code": 17, "comment": 2, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/marketer/index.tsx": {"language": "TypeScript JSX", "code": 79, "comment": 2, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/index.tsx": {"language": "TypeScript JSX", "code": 116, "comment": 2, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/marketer/register/index.tsx": {"language": "TypeScript JSX", "code": 21, "comment": 2, "blank": 5}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/filterControls/index.tsx": {"language": "TypeScript JSX", "code": 729, "comment": 17, "blank": 49}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/housing/%5BadCode%5D.tsx": {"language": "TypeScript JSX", "code": 436, "comment": 21, "blank": 38}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/contacts/index.tsx": {"language": "TypeScript JSX", "code": 132, "comment": 3, "blank": 9}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/housing/unauthorize.tsx": {"language": "TypeScript JSX", "code": 50, "comment": 1, "blank": 6}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/housing/my-ads/index.tsx": {"language": "TypeScript JSX", "code": 48, "comment": 1, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/services/feature/apiSlice.ts": {"language": "TypeScript", "code": 84, "comment": 0, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/authentication/login/index.tsx": {"language": "TypeScript JSX", "code": 105, "comment": 12, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/services/feature/type.ts": {"language": "TypeScript", "code": 0, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/services/baseApi.ts": {"language": "TypeScript", "code": 21, "comment": 0, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/services/auth/types.ts": {"language": "TypeScript", "code": 17, "comment": 0, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/services/auth/apiSlice.ts": {"language": "TypeScript", "code": 60, "comment": 61, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/services/addressesApiSlice.ts": {"language": "TypeScript", "code": 18, "comment": 1, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/housing/ad/index.tsx": {"language": "TypeScript JSX", "code": 77, "comment": 25, "blank": 5}, "file:///d%3A/1-git/prod/dev-soodam/src/pages/housing/ad/new/index.tsx": {"language": "TypeScript JSX", "code": 87, "comment": 3, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/store/index.ts": {"language": "TypeScript", "code": 34, "comment": 4, "blank": 6}, "file:///d%3A/1-git/prod/dev-soodam/src/services/allHousing/apiSlice.ts": {"language": "TypeScript", "code": 49, "comment": 0, "blank": 6}, "file:///d%3A/1-git/prod/dev-soodam/src/mocks/server.ts": {"language": "TypeScript", "code": 3, "comment": 0, "blank": 2}, "file:///d%3A/1-git/prod/dev-soodam/src/services/allHousing/types.ts": {"language": "TypeScript", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/mocks/browser.ts": {"language": "TypeScript", "code": 3, "comment": 0, "blank": 2}, "file:///d%3A/1-git/prod/dev-soodam/src/hooks/useRedux.ts": {"language": "TypeScript", "code": 4, "comment": 0, "blank": 2}, "file:///d%3A/1-git/prod/dev-soodam/src/hooks/useDebounce.ts": {"language": "TypeScript", "code": 12, "comment": 0, "blank": 6}, "file:///d%3A/1-git/prod/dev-soodam/src/mocks/handlers.ts": {"language": "TypeScript", "code": 2146, "comment": 144, "blank": 118}, "file:///d%3A/1-git/prod/dev-soodam/src/hooks/useDisclosure.ts": {"language": "TypeScript", "code": 14, "comment": 1, "blank": 6}, "file:///d%3A/1-git/prod/dev-soodam/src/hooks/useChangeRoute.ts": {"language": "TypeScript", "code": 14, "comment": 0, "blank": 7}, "file:///d%3A/1-git/prod/dev-soodam/src/hooks/use-filter.ts": {"language": "TypeScript", "code": 45, "comment": 0, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/hooks/index.ts": {"language": "TypeScript", "code": 4, "comment": 2, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/index.ts": {"language": "TypeScript", "code": 183, "comment": 1, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/components/user/SubscriptionCard.tsx": {"language": "TypeScript JSX", "code": 23, "comment": 3, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/components/user/ProtectedRouteWrapper.tsx": {"language": "TypeScript JSX", "code": 0, "comment": 24, "blank": 10}, "file:///d%3A/1-git/prod/dev-soodam/src/components/user/LogoutButton.tsx": {"language": "TypeScript JSX", "code": 29, "comment": 2, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/TextFiledPrice.tsx": {"language": "TypeScript JSX", "code": 90, "comment": 6, "blank": 10}, "file:///d%3A/1-git/prod/dev-soodam/src/components/user/index.ts": {"language": "TypeScript", "code": 2, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/Skeleton.tsx": {"language": "TypeScript JSX", "code": 63, "comment": 4, "blank": 13}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/TextField.tsx": {"language": "TypeScript JSX", "code": 73, "comment": 6, "blank": 11}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/SelectBox.tsx": {"language": "TypeScript JSX", "code": 90, "comment": 7, "blank": 12}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/Modal.tsx": {"language": "TypeScript JSX", "code": 143, "comment": 4, "blank": 18}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/loading/PageTransitionLoading.tsx": {"language": "TypeScript JSX", "code": 0, "comment": 32, "blank": 7}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/loading/LoadingScreen.tsx": {"language": "TypeScript JSX", "code": 16, "comment": 1, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/loading/InlineLoading.tsx": {"language": "TypeScript JSX", "code": 68, "comment": 1, "blank": 0}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/loading/FullScreenLoading.tsx": {"language": "TypeScript JSX", "code": 8, "comment": 0, "blank": 2}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/loading/FullInlineLoading.tsx": {"language": "TypeScript JSX", "code": 68, "comment": 0, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/index.ts": {"language": "TypeScript", "code": 13, "comment": 1, "blank": 5}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/DisplayError.tsx": {"language": "TypeScript JSX", "code": 70, "comment": 0, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/Buttons.tsx": {"language": "TypeScript JSX", "code": 31, "comment": 2, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/Checkbox.tsx": {"language": "TypeScript JSX", "code": 45, "comment": 0, "blank": 7}, "file:///d%3A/1-git/prod/dev-soodam/src/components/ui/Alert.tsx": {"language": "TypeScript JSX", "code": 53, "comment": 4, "blank": 7}, "file:///d%3A/1-git/prod/dev-soodam/public/globe.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Frame.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/public/file.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/eye.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/clock2.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Eliipse.svg": {"language": "XML", "code": 15, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/close-circle.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/check.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/change.svg": {"language": "XML", "code": 8, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/chage.svg": {"language": "XML", "code": 8, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/card-tick.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/camera2.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/camera.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/calendar.svg": {"language": "XML", "code": 12, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/package-lock.json": {"language": "JSON", "code": 12844, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/camera-small.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/call.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/calendar-tick.svg": {"language": "XML", "code": 11, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/calendar-search.svg": {"language": "XML", "code": 11, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/button.svg": {"language": "XML", "code": 17, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/buliding.svg": {"language": "XML", "code": 10, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Bed.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/buliding%20%281%29.svg": {"language": "XML", "code": 10, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/battery-full.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/arrow-left.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/archive-tick2.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/gear-settings.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/archive-tick.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/add-circle.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/3dcube2.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/3d-rotate.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/3d-cube-scan.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/20016826761595759881%201.svg": {"language": "XML", "code": 18, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/sliders/index.ts": {"language": "TypeScript", "code": 1, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/18237869081579156621%201.svg": {"language": "XML", "code": 9, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/sliders/HousingSliders.tsx": {"language": "TypeScript JSX", "code": 73, "comment": 5, "blank": 5}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/1017280091582962144%20%281%29%201.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/one-finger-swipe-left%20%282%29.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/13724094091679983182%201.svg": {"language": "XML", "code": 35, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/more.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/note-text.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/one-finger-swipe-left%20%281%29.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/note-favorite.svg": {"language": "XML", "code": 9, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/notification-bing.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/menu.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/mirror%20%281%29.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/map.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/map%20copy.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/logout.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/locationred.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/map%20%281%29.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/location22.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/location.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/info-circle.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/location2.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/info-circle%20%281%29.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/home.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/hospital.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/house.svg": {"language": "XML", "code": 8, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/home-2.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/heart%201.svg": {"language": "XML", "code": 10, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Group%207.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/headphone.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Group.svg": {"language": "XML", "code": 36, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Group%201000011157.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Group%201000011001.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/skeleton/NavbarSkeleton.tsx": {"language": "TypeScript JSX", "code": 11, "comment": 0, "blank": 2}, "file:///d%3A/1-git/prod/dev-soodam/src/components/skeleton/index.ts": {"language": "TypeScript", "code": 2, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/shared/index.ts": {"language": "TypeScript", "code": 8, "comment": 0, "blank": 0}, "file:///d%3A/1-git/prod/dev-soodam/src/components/shared/HandleResponse.tsx": {"language": "TypeScript JSX", "code": 29, "comment": 15, "blank": 14}, "file:///d%3A/1-git/prod/dev-soodam/src/components/skeleton/HousingSkeleton.tsx": {"language": "TypeScript JSX", "code": 26, "comment": 0, "blank": 2}, "file:///d%3A/1-git/prod/dev-soodam/src/components/shared/Header.tsx": {"language": "TypeScript JSX", "code": 77, "comment": 0, "blank": 8}, "file:///d%3A/1-git/prod/dev-soodam/src/components/shared/Footer.tsx": {"language": "TypeScript JSX", "code": 7, "comment": 0, "blank": 2}, "file:///d%3A/1-git/prod/dev-soodam/src/components/shared/FilterControlsHeader.tsx": {"language": "TypeScript JSX", "code": 59, "comment": 1, "blank": 5}, "file:///d%3A/1-git/prod/dev-soodam/src/components/shared/FilterControlNavBar.tsx": {"language": "TypeScript JSX", "code": 156, "comment": 9, "blank": 21}, "file:///d%3A/1-git/prod/dev-soodam/src/components/shared/MapMode.tsx": {"language": "TypeScript JSX", "code": 24, "comment": 0, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/components/shared/DataStateDisplay.tsx": {"language": "TypeScript JSX", "code": 49, "comment": 2, "blank": 7}, "file:///d%3A/1-git/prod/dev-soodam/src/components/shared/BottomNavigation.tsx": {"language": "TypeScript JSX", "code": 77, "comment": 0, "blank": 6}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/grid-2.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/gps.svg": {"language": "XML", "code": 8, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/video.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/warning-22.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/wallet.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Vector.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/shared/Sidebar.tsx": {"language": "TypeScript JSX", "code": 211, "comment": 1, "blank": 10}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Vector%20395.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Vector%20%282%29.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Vector%20%281%29.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/user-edit2.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/user-edit.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/ticket-star.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/ticket-star2.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Upload%20icon.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/ticket-discount.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/sms-edit.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/task-square.svg": {"language": "XML", "code": 7, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/sms-edit1.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/Send.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/search.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/repeat.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/search-normal.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/profile-tick.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/profile-tick-green.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/profile-no-tick.svg": {"language": "XML", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/profile-add.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/map/MapLocationPicker.tsx": {"language": "TypeScript JSX", "code": 539, "comment": 14, "blank": 70}, "file:///d%3A/1-git/prod/dev-soodam/src/components/modals/FirstLoginModal.tsx": {"language": "TypeScript JSX", "code": 91, "comment": 0, "blank": 6}, "file:///d%3A/1-git/prod/dev-soodam/src/components/map/LocationMap.tsx": {"language": "TypeScript JSX", "code": 32, "comment": 0, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/components/map/Map.tsx": {"language": "TypeScript JSX", "code": 1084, "comment": 108, "blank": 111}, "file:///d%3A/1-git/prod/dev-soodam/src/components/modals/index.ts": {"language": "TypeScript", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/modals/MemberUserLoginsModal.tsx": {"language": "TypeScript JSX", "code": 64, "comment": 0, "blank": 4}, "file:///d%3A/1-git/prod/dev-soodam/src/components/modals/SearchModal.tsx": {"language": "TypeScript JSX", "code": 147, "comment": 13, "blank": 10}, "file:///d%3A/1-git/prod/dev-soodam/src/components/layouts/index.ts": {"language": "TypeScript", "code": 1, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/housing/index.ts": {"language": "TypeScript", "code": 1, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/layouts/ClientLayout.tsx": {"language": "TypeScript JSX", "code": 75, "comment": 2, "blank": 10}, "file:///d%3A/1-git/prod/dev-soodam/src/components/housing/HousingFilterControls.tsx": {"language": "TypeScript JSX", "code": 111, "comment": 10, "blank": 24}, "file:///d%3A/1-git/prod/dev-soodam/src/components/housing/HousingCard.tsx": {"language": "TypeScript JSX", "code": 118, "comment": 14, "blank": 11}, "file:///d%3A/1-git/prod/dev-soodam/src/components/emptyList/EmptyCustomList.tsx": {"language": "TypeScript JSX", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/emptyList/EmptySearchList.tsx": {"language": "TypeScript JSX", "code": 14, "comment": 0, "blank": 3}, "file:///d%3A/1-git/prod/dev-soodam/src/components/emptyList/index.ts": {"language": "TypeScript", "code": 2, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/people.svg": {"language": "XML", "code": 8, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/forms/index.ts": {"language": "TypeScript", "code": 5, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/components/forms/housing/AdvertisementRegistrationForm.tsx": {"language": "TypeScript JSX", "code": 1176, "comment": 21, "blank": 69}, "file:///d%3A/1-git/prod/dev-soodam/src/components/forms/housing/RequestRegistrationForm.tsx": {"language": "TypeScript JSX", "code": 1036, "comment": 20, "blank": 52}, "file:///d%3A/1-git/prod/dev-soodam/src/components/forms/login/VerificationCode.tsx": {"language": "TypeScript JSX", "code": 149, "comment": 2, "blank": 19}, "file:///d%3A/1-git/prod/dev-soodam/src/components/forms/login/MarketerUserLoginForm.tsx": {"language": "TypeScript JSX", "code": 592, "comment": 6, "blank": 36}, "file:///d%3A/1-git/prod/dev-soodam/src/components/forms/login/LoginForm.tsx": {"language": "TypeScript JSX", "code": 54, "comment": 3, "blank": 7}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/one-finger-swipe-left.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/1-git/prod/dev-soodam/src/icons/svgs/one-finger-swipe-left%20copy.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}}