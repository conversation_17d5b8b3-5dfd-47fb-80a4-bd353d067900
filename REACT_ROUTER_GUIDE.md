# React Router Integration Guide

This guide explains how to use React Router alongside Next.js in your Soodam frontend project.

## Overview

React Router has been integrated into your Next.js project to provide additional client-side routing capabilities while maintaining compatibility with Next.js's built-in routing system.

## Key Components and Utilities

### 1. Route Configuration (`src/utils/routerConfig.ts`)

- **ROUTES**: Object containing all route paths as constants
- **routeConfig**: Route configuration for React Router
- **getRoutePath()**: Helper function to get route paths

```typescript
import { ROUTES, getRoutePath } from '@/utils'

// Use route constants
const homePath = ROUTES.HOME
const adminPath = getRoutePath('ADMIN')
```

### 2. RouterProvider (`src/components/router/RouterProvider.tsx`)

Wraps your application with React Router's BrowserRouter and syncs with Next.js routing.

### 3. Custom Hook (`src/hooks/useReactRouter.ts`)

Provides React Router functionality with additional helpers:

```typescript
import { useReactRouter } from '@/hooks'

const MyComponent = () => {
  const { navigate, location, params, goToRoute, isCurrentRoute } = useReactRouter()

  // Navigate programmatically
  const handleClick = () => {
    navigate('/admin')
    // or
    goToRoute('ADMIN')
  }

  // Check current route
  const isHome = isCurrentRoute('HOME')

  return (
    <div>
      <p>Current path: {location.pathname}</p>
      <button onClick={handleClick}>Go to Admin</button>
    </div>
  )
}
```

### 4. RouterLink Component (`src/components/router/RouterLink.tsx`)

Enhanced Link component that can use either React Router or Next.js routing:

```typescript
import { RouterLink, RouteLink } from '@/components/router'

// React Router Link
<RouterLink to="/admin" className="nav-link">
  Admin
</RouterLink>

// Next.js Link
<RouterLink to="/admin" useNextRouter={true} className="nav-link">
  Admin (Next.js)
</RouterLink>

// Route-specific Link
<RouteLink route="ADMIN" className="nav-link">
  Admin
</RouteLink>
```

## Usage Examples

### Basic Navigation

```typescript
import { useReactRouter } from '@/hooks'
import { ROUTES } from '@/utils'

const Navigation = () => {
  const { navigate, isCurrentRoute } = useReactRouter()

  return (
    <nav>
      <button 
        onClick={() => navigate(ROUTES.HOME)}
        className={isCurrentRoute('HOME') ? 'active' : ''}
      >
        Home
      </button>
      <button 
        onClick={() => navigate(ROUTES.ADMIN)}
        className={isCurrentRoute('ADMIN') ? 'active' : ''}
      >
        Admin
      </button>
    </nav>
  )
}
```

### Using Links

```typescript
import { RouterLink } from '@/components/router'
import { ROUTES } from '@/utils'

const Menu = () => (
  <div>
    <RouterLink to={ROUTES.HOME}>Home</RouterLink>
    <RouterLink to={ROUTES.ADMIN}>Admin</RouterLink>
    <RouterLink to={ROUTES.CONTACTS}>Contacts</RouterLink>
  </div>
)
```

### Route Parameters

```typescript
import { useReactRouter } from '@/hooks'

const HousingDetail = () => {
  const { params } = useReactRouter()
  const housingId = params.id

  return <div>Housing ID: {housingId}</div>
}
```

## Available Routes

All routes are defined in `src/utils/routerConfig.ts`:

- `HOME`: `/`
- `ADMIN`: `/admin`
- `ADMIN_AUTH`: `/admin/authentication`
- `AUTHENTICATION`: `/authentication`
- `CONTACTS`: `/contacts`
- `ESTATE_CONSULTANT`: `/estate-consultant`
- `HOUSING`: `/housing`
- `LISTS`: `/lists`
- `MARKETER`: `/marketer`
- `MEMBERSHIP`: `/memberShip`
- `MY_PAYMENTS`: `/my-payments`
- `MY_CITY`: `/myCity`
- `NEWS`: `/news`
- `REQUESTS`: `/requests`
- `SOODAM`: `/soodam`
- `SUBSCRIPTION`: `/subscription`
- `VISIT_STATISTICS`: `/visitStatistics`

## Best Practices

1. **Use route constants**: Always use `ROUTES` constants instead of hardcoded strings
2. **Choose the right tool**: Use React Router for complex client-side navigation, Next.js router for server-side routing
3. **Type safety**: Leverage TypeScript types for route paths
4. **Consistent navigation**: Use the provided hooks and components for consistent behavior

## Integration with Existing Code

The React Router integration is designed to work alongside your existing Next.js setup:

- Next.js routing continues to work as before
- React Router provides additional client-side navigation capabilities
- Both systems can be used together based on your needs
- The `useReactRouter` hook provides access to both React Router and Next.js router

## Testing

To test the React Router integration, you can use the example component:

```typescript
import { ReactRouterExample } from '@/components/examples'

// Add this to any page to test React Router functionality
<ReactRouterExample />
```

This component demonstrates all the React Router features and provides a visual way to test navigation.
