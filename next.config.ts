/** @type {import('next').NextConfig} */
const nextConfig: import('next').NextConfig = {
  // App Router is enabled by default in Next.js 13+
  experimental: {
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      issuer: /\.[jt]sx?$/,
      use: [{
        loader: '@svgr/webpack',
        options: {
          typescript: true,
          icon: true
        }
      }]
    });
    return config;
  },
  images: {
    domains: ['localhost', '*************'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*************',
      },
    ],
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://*************:8000/api/:path*'
      },
      {
        source: '/media/:path*',
        destination: 'http://*************:8000/media/:path*'
      } 
    ];
  },

}

module.exports = nextConfig