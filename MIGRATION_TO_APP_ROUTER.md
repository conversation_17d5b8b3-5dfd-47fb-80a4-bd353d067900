# Migration to Next.js App Router (Option 2)

## Why App Router?

Next.js 13+ App Router provides:
- **Better Performance**: Automatic code splitting and streaming
- **Improved DX**: Co-located layouts, loading states, error boundaries
- **Modern React**: Server Components, Suspense, concurrent features
- **Better SEO**: Enhanced metadata and OpenGraph support

## Migration Steps

### 1. Create App Directory Structure
```
src/
  app/
    layout.tsx                 # Root layout
    page.tsx                   # Home page
    loading.tsx                # Global loading UI
    error.tsx                  # Global error UI
    not-found.tsx             # 404 page
    
    admin/
      layout.tsx              # Admin layout
      page.tsx                # Admin dashboard
      authentication/
        login/
          page.tsx            # Login page
      messages/
        page.tsx              # Messages page
    
    housing/
      [id]/
        page.tsx              # Housing detail
      ad/
        page.tsx              # Housing ads
        new/
          page.tsx            # New housing ad
    
    contacts/
      page.tsx                # Contacts page
```

### 2. Enhanced Navigation Hook
```typescript
// src/hooks/useAppNavigation.ts
'use client'
import { useRouter, usePathname } from 'next/navigation'
import { ROUTES } from '@/utils/routes'

export const useAppNavigation = () => {
  const router = useRouter()
  const pathname = usePathname()

  const navigateTo = (path: string, options?: { replace?: boolean }) => {
    if (options?.replace) {
      router.replace(path)
    } else {
      router.push(path)
    }
  }

  const isCurrentRoute = (route: string): boolean => {
    return pathname === route || pathname.startsWith(route + '/')
  }

  return {
    navigateTo,
    isCurrentRoute,
    pathname,
    router,
  }
}
```

### 3. Server Components Benefits
```typescript
// src/app/housing/[id]/page.tsx
import { getHousingDetail } from '@/services/housing'

interface Props {
  params: { id: string }
}

// Server Component - runs on server, better SEO
export default async function HousingDetailPage({ params }: Props) {
  const housing = await getHousingDetail(params.id)
  
  return (
    <div>
      <h1>{housing.title}</h1>
      {/* Housing details */}
    </div>
  )
}

// Generate metadata for SEO
export async function generateMetadata({ params }: Props) {
  const housing = await getHousingDetail(params.id)
  
  return {
    title: housing.title,
    description: housing.description,
    openGraph: {
      title: housing.title,
      description: housing.description,
      images: [housing.image],
    },
  }
}
```

## Benefits Over Current Implementation

1. **No Dual Router Complexity**: Single routing system
2. **Better Performance**: Automatic optimizations
3. **Modern React Features**: Server Components, Suspense
4. **Improved SEO**: Better metadata handling
5. **Simpler Architecture**: File-based routing with layouts
