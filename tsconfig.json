{
  "compilerOptions": {
    "target": "es2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    // "strict": true,
    "strict": false,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "paths": {
      "@/*": ["./src/*"]
    },
    "typeRoots": ["./node_modules/@types", "./declarations.d.ts"]
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "next.config.ts",],
  "exclude": ["node_modules"]
}
