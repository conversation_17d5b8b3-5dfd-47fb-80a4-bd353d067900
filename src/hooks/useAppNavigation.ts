'use client'

import { useRouter, usePathname, useSearchParams } from 'next/navigation'
import { useCallback } from 'react'

// Modern route constants for App Router
export const ROUTES = {
  HOME: '/',
  ADMIN: '/admin',
  ADMIN_AUTH: '/admin/authentication',
  ADMIN_LOGIN: '/admin/authentication/login',
  ADMIN_MESSAGES: '/admin/messages',
  ADMIN_SEARCH: '/admin/search',
  ADMIN_SETTINGS: '/admin/settings',
  ADMIN_ADVERTISEMENTS: '/admin/advertisements',
  AUTHENTICATION: '/authentication',
  AUTHENTICATION_LOGIN: '/authentication/login',
  CONTACTS: '/contacts',
  ESTATE_CONSULTANT: '/estate-consultant',
  ESTATE_CONSULTANT_REGISTER: '/estate-consultant/register',
  FILTER_CONTROLS: '/filterControls',
  HOUSING: '/housing',
  HOUSING_AD: '/housing/ad',
  HOUSING_AD_NEW: '/housing/ad/new',
  HOUSING_MY_ADS: '/housing/my-ads',
  HOUSING_UNAUTHORIZED: '/housing/unauthorize',
  LISTS: '/lists',
  MARKETER: '/marketer',
  MARKETER_REGISTER: '/marketer/register',
  MEMBERSHIP: '/memberShip',
  MY_PAYMENTS: '/my-payments',
  MY_CITY: '/myCity',
  NEWS: '/news',
  REQUESTS: '/requests',
  REQUESTS_NEW: '/requests/new',
  SOODAM: '/soodam',
  SOODAM_ACCOUNT: '/soodam/account',
  SOODAM_SETTING: '/soodam/setting',
  SUBSCRIPTION: '/subscription',
  VISIT_STATISTICS: '/visitStatistics',
} as const

export type RoutePath = typeof ROUTES[keyof typeof ROUTES]

interface NavigationOptions {
  replace?: boolean
  scroll?: boolean
}

export const useAppNavigation = () => {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // Navigate to a route
  const navigateTo = useCallback((
    path: string,
    options: NavigationOptions = {}
  ) => {
    const { replace = false, scroll = true } = options
    
    if (replace) {
      router.replace(path, { scroll })
    } else {
      router.push(path, { scroll })
    }
  }, [router])

  // Quick navigation methods
  const goHome = useCallback(() => navigateTo(ROUTES.HOME), [navigateTo])
  const goToAdmin = useCallback(() => navigateTo(ROUTES.ADMIN), [navigateTo])
  const goToContacts = useCallback(() => navigateTo(ROUTES.CONTACTS), [navigateTo])
  
  // Dynamic route navigation
  const goToHousingDetail = useCallback((id: string | number) => {
    navigateTo(`/housing/${id}`)
  }, [navigateTo])

  const goToEstateConsultant = useCallback((id: string | number) => {
    navigateTo(`/estate-consultant/${id}`)
  }, [navigateTo])

  // Route checking
  const isCurrentRoute = useCallback((route: string): boolean => {
    if (route === ROUTES.HOME) {
      return pathname === ROUTES.HOME
    }
    return pathname === route || pathname.startsWith(route + '/')
  }, [pathname])

  // Back navigation
  const goBack = useCallback(() => {
    router.back()
  }, [router])

  // Forward navigation
  const goForward = useCallback(() => {
    router.forward()
  }, [router])

  // Refresh current page
  const refresh = useCallback(() => {
    router.refresh()
  }, [router])

  // Build URL with search params
  const buildUrl = useCallback((path: string, params?: Record<string, string>) => {
    if (!params) return path
    
    const url = new URL(path, window.location.origin)
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.set(key, value)
    })
    return url.pathname + url.search
  }, [])

  // Navigate with search params
  const navigateWithParams = useCallback((
    path: string,
    params: Record<string, string>,
    options: NavigationOptions = {}
  ) => {
    const url = buildUrl(path, params)
    navigateTo(url, options)
  }, [navigateTo, buildUrl])

  return {
    // Navigation
    navigateTo,
    navigateWithParams,
    goHome,
    goToAdmin,
    goToContacts,
    goToHousingDetail,
    goToEstateConsultant,
    goBack,
    goForward,
    refresh,
    
    // Route checking
    isCurrentRoute,
    pathname,
    searchParams,
    
    // Utilities
    buildUrl,
    
    // Original router for advanced use cases
    router,
  }
}

export default useAppNavigation
