// Enhanced Next.js navigation hook - BETTER APPROACH
import { useRouter } from 'next/router'
import { useCallback } from 'react'
import { ROUTES, RoutePath, buildRoute, isActiveRoute } from '@/utils/nextRoutes'

interface NavigationOptions {
  replace?: boolean
  shallow?: boolean
  scroll?: boolean
}

export const useNextNavigation = () => {
  const router = useRouter()

  // Type-safe navigation
  const navigateTo = useCallback((
    path: RoutePath | string,
    options: NavigationOptions = {}
  ) => {
    const { replace = false, ...routerOptions } = options
    
    if (replace) {
      return router.replace(path, undefined, routerOptions)
    }
    return router.push(path, undefined, routerOptions)
  }, [router])

  // Quick navigation methods
  const goHome = useCallback(() => navigateTo(ROUTES.HOME), [navigateTo])
  const goToAdmin = useCallback(() => navigateTo(ROUTES.ADMIN), [navigateTo])
  const goToContacts = useCallback(() => navigateTo(ROUTES.CONTACTS), [navigateTo])
  
  // Dynamic route navigation
  const goToHousingDetail = useCallback((id: string | number) => {
    navigateTo(buildRoute.housingDetail(id))
  }, [navigateTo])

  const goToEstateConsultant = useCallback((id: string | number) => {
    navigateTo(buildRoute.estateConsultantDetail(id))
  }, [navigateTo])

  // Route checking
  const isCurrentRoute = useCallback((route: RoutePath): boolean => {
    return isActiveRoute(router.pathname, route)
  }, [router.pathname])

  // Back navigation
  const goBack = useCallback(() => {
    if (window.history.length > 1) {
      router.back()
    } else {
      navigateTo(ROUTES.HOME)
    }
  }, [router, navigateTo])

  // Prefetch routes
  const prefetchRoute = useCallback((route: RoutePath) => {
    router.prefetch(route)
  }, [router])

  return {
    // Navigation
    navigateTo,
    goHome,
    goToAdmin,
    goToContacts,
    goToHousingDetail,
    goToEstateConsultant,
    goBack,
    
    // Route checking
    isCurrentRoute,
    currentPath: router.pathname,
    query: router.query,
    
    // Utilities
    prefetchRoute,
    
    // Original router for advanced use cases
    router,
  }
}

export default useNextNavigation
