import { useNavigate, useLocation, useParams } from 'react-router-dom'
import { useRouter as useNextRouter } from 'next/router'
import { ROUTES, RoutePath } from '@/utils/routerConfig'

interface UseReactRouterReturn {
  // React Router navigation
  navigate: (to: string | RoutePath, options?: { replace?: boolean; state?: any }) => void
  location: ReturnType<typeof useLocation>
  params: ReturnType<typeof useParams>
  
  // Next.js router for compatibility
  nextRouter: ReturnType<typeof useNextRouter>
  
  // Helper functions
  goToRoute: (route: keyof typeof ROUTES, options?: { replace?: boolean }) => void
  isCurrentRoute: (route: keyof typeof ROUTES) => boolean
}

export const useReactRouter = (): UseReactRouterReturn => {
  const navigate = useNavigate()
  const location = useLocation()
  const params = useParams()
  const nextRouter = useNextRouter()

  const goToRoute = (route: keyof typeof ROUTES, options?: { replace?: boolean }) => {
    const path = ROUTES[route]
    navigate(path, options)
  }

  const isCurrentRoute = (route: keyof typeof ROUTES): boolean => {
    const routePath = ROUTES[route]
    return location.pathname === routePath || location.pathname.startsWith(routePath + '/')
  }

  return {
    navigate,
    location,
    params,
    nextRouter,
    goToRoute,
    isCurrentRoute,
  }
}

export default useReactRouter
