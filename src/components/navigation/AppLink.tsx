'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ROUTES } from '@/hooks/useAppNavigation'

interface AppLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  activeClassName?: string
  exact?: boolean
  prefetch?: boolean
  replace?: boolean
  scroll?: boolean
  onClick?: () => void
}

export const AppLink: React.FC<AppLinkProps> = ({
  href,
  children,
  className = '',
  activeClassName = '',
  exact = false,
  prefetch = true,
  replace = false,
  scroll = true,
  onClick,
}) => {
  const pathname = usePathname()

  const isActive = exact
    ? pathname === href
    : pathname === href || pathname.startsWith(href + '/')

  const finalClassName = `${className} ${isActive ? activeClassName : ''}`.trim()

  return (
    <Link
      href={href}
      className={finalClassName}
      prefetch={prefetch}
      replace={replace}
      scroll={scroll}
      onClick={onClick}
    >
      {children}
    </Link>
  )
}

// Specific route components for better type safety
interface RouteLink {
  children: React.ReactNode
  className?: string
  activeClassName?: string
}

export const HomeLink: React.FC<RouteLink> = (props) => (
  <AppLink href={ROUTES.HOME} {...props} exact />
)

export const AdminLink: React.FC<RouteLink> = (props) => (
  <AppLink href={ROUTES.ADMIN} {...props} />
)

export const ContactsLink: React.FC<RouteLink> = (props) => (
  <AppLink href={ROUTES.CONTACTS} {...props} exact />
)

export const HousingLink: React.FC<RouteLink> = (props) => (
  <AppLink href={ROUTES.HOUSING} {...props} />
)

// Dynamic route links
interface DynamicRouteLink extends RouteLink {
  id: string | number
}

export const HousingDetailLink: React.FC<DynamicRouteLink> = ({ id, ...props }) => (
  <AppLink href={`/housing/${id}`} {...props} exact />
)

export const EstateConsultantDetailLink: React.FC<DynamicRouteLink> = ({ id, ...props }) => (
  <AppLink href={`/estate-consultant/${id}`} {...props} exact />
)

export default AppLink
