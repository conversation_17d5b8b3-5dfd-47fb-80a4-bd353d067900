// Enhanced Link component for Next.js - BETTER APPROACH
import React from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { ROUTES, RoutePath, isActiveRoute } from '@/utils/nextRoutes'

interface AppLinkProps {
  href: RoutePath | string
  children: React.ReactNode
  className?: string
  activeClassName?: string
  exact?: boolean
  prefetch?: boolean
  replace?: boolean
  scroll?: boolean
  shallow?: boolean
  onClick?: () => void
}

export const AppLink: React.FC<AppLinkProps> = ({
  href,
  children,
  className = '',
  activeClassName = '',
  exact = false,
  prefetch = true,
  replace = false,
  scroll = true,
  shallow = false,
  onClick,
}) => {
  const router = useRouter()
  
  const isActive = exact 
    ? router.pathname === href
    : isActiveRoute(router.pathname, href as RoutePath)

  const finalClassName = `${className} ${isActive ? activeClassName : ''}`.trim()

  return (
    <Link
      href={href}
      className={finalClassName}
      prefetch={prefetch}
      replace={replace}
      scroll={scroll}
      shallow={shallow}
      onClick={onClick}
    >
      {children}
    </Link>
  )
}

// Specific route components for better type safety
interface RouteLink {
  children: React.ReactNode
  className?: string
  activeClassName?: string
}

export const HomeLink: React.FC<RouteLink> = (props) => (
  <AppLink href={ROUTES.HOME} {...props} exact />
)

export const AdminLink: React.FC<RouteLink> = (props) => (
  <AppLink href={ROUTES.ADMIN} {...props} />
)

export const ContactsLink: React.FC<RouteLink> = (props) => (
  <AppLink href={ROUTES.CONTACTS} {...props} exact />
)

// Dynamic route links
interface DynamicRouteLink extends RouteLink {
  id: string | number
}

export const HousingDetailLink: React.FC<DynamicRouteLink> = ({ id, ...props }) => (
  <AppLink href={`/housing/${id}`} {...props} exact />
)

export const EstateConsultantDetailLink: React.FC<DynamicRouteLink> = ({ id, ...props }) => (
  <AppLink href={`/estate-consultant/${id}`} {...props} exact />
)

export default AppLink
