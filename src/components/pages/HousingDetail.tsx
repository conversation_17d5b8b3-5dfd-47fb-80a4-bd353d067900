'use client'

import React from 'react'
import { useAppNavigation } from '@/hooks/useAppNavigation'
import { AppLink } from '@/components/navigation/AppLink'

interface HousingData {
  id: string
  title: string
  description: string
  price: string
  location: string
  image: string
}

interface Props {
  housing: HousingData
}

export const HousingDetailContent: React.FC<Props> = ({ housing }) => {
  const { goBack } = useAppNavigation()

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <button
          onClick={goBack}
          className="mb-6 flex items-center text-blue-600 hover:text-blue-800"
        >
          ← Back to Housing
        </button>

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="h-64 bg-gray-200 flex items-center justify-center">
            <span className="text-gray-500">Housing Image Placeholder</span>
          </div>
          
          <div className="p-6">
            <h1 className="text-3xl font-bold mb-4">{housing.title}</h1>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h2 className="text-xl font-semibold mb-3">Property Details</h2>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Price:</span>
                    <span className="font-semibold">{housing.price} تومان</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Location:</span>
                    <span>{housing.location}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Property ID:</span>
                    <span>{housing.id}</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h2 className="text-xl font-semibold mb-3">Contact Information</h2>
                <div className="space-y-2">
                  <button className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors">
                    Contact Owner
                  </button>
                  <button className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition-colors">
                    Schedule Visit
                  </button>
                </div>
              </div>
            </div>
            
            <div>
              <h2 className="text-xl font-semibold mb-3">Description</h2>
              <p className="text-gray-700 leading-relaxed">{housing.description}</p>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <h2 className="text-2xl font-semibold mb-4">Similar Properties</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map((id) => (
              <div key={id} className="bg-white rounded-lg shadow-md p-4">
                <div className="bg-gray-200 h-32 rounded mb-3"></div>
                <h3 className="font-semibold mb-2">Similar Property {id}</h3>
                <p className="text-gray-600 text-sm mb-3">
                  Another great property option
                </p>
                <AppLink 
                  href={`/housing/${id + 100}`}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  View Details →
                </AppLink>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
