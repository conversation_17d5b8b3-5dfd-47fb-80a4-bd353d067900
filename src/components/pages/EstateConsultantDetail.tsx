'use client'

import React from 'react'
import { useAppNavigation } from '@/hooks/useAppNavigation'

interface ConsultantData {
  id: string
  name: string
  description: string
  experience: string
  location: string
  rating: number
  image: string
}

interface Props {
  consultant: ConsultantData
}

export const EstateConsultantDetailContent: React.FC<Props> = ({ consultant }) => {
  const { goBack } = useAppNavigation()

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <button
          onClick={goBack}
          className="mb-6 flex items-center text-blue-600 hover:text-blue-800"
        >
          ← Back to Consultants
        </button>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center mb-6">
            <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
              <span className="text-2xl font-semibold text-gray-600">
                {consultant.name.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
            <div className="ml-6">
              <h1 className="text-3xl font-bold">{consultant.name}</h1>
              <p className="text-gray-600">{consultant.location}</p>
              <div className="flex items-center mt-2">
                <span className="text-yellow-500">⭐</span>
                <span className="ml-1 font-semibold">{consultant.rating}</span>
                <span className="ml-2 text-gray-600">({consultant.experience})</span>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h2 className="text-xl font-semibold mb-3">About</h2>
              <p className="text-gray-700 leading-relaxed">{consultant.description}</p>
            </div>
            
            <div>
              <h2 className="text-xl font-semibold mb-3">Contact</h2>
              <div className="space-y-3">
                <button className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors">
                  Send Message
                </button>
                <button className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition-colors">
                  Schedule Consultation
                </button>
                <button className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-50 transition-colors">
                  View Contact Info
                </button>
              </div>
            </div>
          </div>
          
          <div className="border-t pt-6">
            <h2 className="text-xl font-semibold mb-4">Specialties & Services</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Property Types</h3>
                <ul className="space-y-1 text-gray-600">
                  <li>• Residential Properties</li>
                  <li>• Commercial Real Estate</li>
                  <li>• Luxury Homes</li>
                  <li>• Investment Properties</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Services</h3>
                <ul className="space-y-1 text-gray-600">
                  <li>• Property Valuation</li>
                  <li>• Market Analysis</li>
                  <li>• Negotiation Support</li>
                  <li>• Legal Assistance</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Recent Reviews</h2>
          <div className="space-y-4">
            {[1, 2, 3].map((review) => (
              <div key={review} className="border-b pb-4 last:border-b-0">
                <div className="flex items-center mb-2">
                  <span className="font-semibold">Client {review}</span>
                  <span className="ml-2 text-yellow-500">⭐⭐⭐⭐⭐</span>
                </div>
                <p className="text-gray-600">
                  Excellent service and professional guidance throughout the entire process.
                  Highly recommended for anyone looking for real estate assistance.
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
