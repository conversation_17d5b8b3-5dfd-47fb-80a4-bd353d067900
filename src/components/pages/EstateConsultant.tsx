'use client'

import React from 'react'
import { EstateConsultantDetailLink } from '@/components/navigation/AppLink'

export const EstateConsultantContent: React.FC = () => {
  const consultants = [
    {
      id: 1,
      name: '<PERSON>',
      experience: '8+ years',
      rating: 4.9,
      specialties: ['Residential', 'Commercial'],
      location: 'Tehran',
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      experience: '5+ years',
      rating: 4.7,
      specialties: ['Luxury Homes', 'Investment'],
      location: 'Isfahan',
    },
    {
      id: 3,
      name: '<PERSON>',
      experience: '10+ years',
      rating: 4.8,
      specialties: ['Commercial', 'Industrial'],
      location: 'Mashhad',
    },
    {
      id: 4,
      name: '<PERSON><PERSON><PERSON>',
      experience: '6+ years',
      rating: 4.6,
      specialties: ['Residential', 'First-time Buyers'],
      location: 'Shiraz',
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Estate Consultants</h1>
        
        <div className="mb-6">
          <div className="flex flex-wrap gap-4">
            <select className="px-4 py-2 border border-gray-300 rounded-md">
              <option>All Locations</option>
              <option>Tehran</option>
              <option>Isfahan</option>
              <option>Mashhad</option>
              <option>Shiraz</option>
            </select>
            <select className="px-4 py-2 border border-gray-300 rounded-md">
              <option>All Specialties</option>
              <option>Residential</option>
              <option>Commercial</option>
              <option>Luxury Homes</option>
              <option>Investment</option>
            </select>
            <select className="px-4 py-2 border border-gray-300 rounded-md">
              <option>Sort by Rating</option>
              <option>Sort by Experience</option>
              <option>Sort by Name</option>
            </select>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {consultants.map((consultant) => (
            <div key={consultant.id} className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-4">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-xl font-semibold text-gray-600">
                    {consultant.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold">{consultant.name}</h3>
                  <p className="text-gray-600">{consultant.location}</p>
                </div>
              </div>
              
              <div className="space-y-2 mb-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Experience:</span>
                  <span className="font-semibold">{consultant.experience}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Rating:</span>
                  <span className="font-semibold">⭐ {consultant.rating}</span>
                </div>
              </div>
              
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">Specialties:</p>
                <div className="flex flex-wrap gap-1">
                  {consultant.specialties.map((specialty) => (
                    <span
                      key={specialty}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                    >
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>
              
              <EstateConsultantDetailLink
                id={consultant.id}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors text-center block"
              >
                View Profile
              </EstateConsultantDetailLink>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
