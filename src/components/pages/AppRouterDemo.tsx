'use client'

import React from 'react'
import { useAppNavigation, ROUTES } from '@/hooks/useAppNavigation'
import { AppLink, HomeLink, AdminLink, ContactsLink, HousingDetailLink } from '@/components/navigation/AppLink'

export const AppRouterDemoContent: React.FC = () => {
  const { navigateTo, pathname, isCurrentRoute, goToHousingDetail, goToEstateConsultant } = useAppNavigation()

  const handleNavigateToHome = () => {
    navigateTo(ROUTES.HOME)
  }

  const handleNavigateToAdmin = () => {
    navigateTo(ROUTES.ADMIN)
  }

  const handleNavigateWithReplace = () => {
    navigateTo(ROUTES.CONTACTS, { replace: true })
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">
          Next.js App Router Demo
        </h1>

        {/* Current location info */}
        <div className="mb-6 p-4 bg-blue-50 rounded border-l-4 border-blue-500">
          <h3 className="text-lg font-semibold mb-2">App Router Features:</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>✅ File-based routing with layouts</li>
            <li>✅ Server Components by default</li>
            <li>✅ Automatic code splitting</li>
            <li>✅ Built-in SEO optimization</li>
            <li>✅ Streaming and Suspense</li>
            <li>✅ Enhanced metadata handling</li>
          </ul>
        </div>

        <div className="mb-6 p-4 bg-gray-100 rounded">
          <h3 className="text-lg font-semibold mb-2">Current Location Info:</h3>
          <p><strong>Pathname:</strong> {pathname}</p>
        </div>

        {/* Navigation buttons */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Programmatic Navigation:</h3>
          <div className="space-x-2">
            <button
              onClick={handleNavigateToHome}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Navigate to Home
            </button>
            <button
              onClick={handleNavigateToAdmin}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Go to Admin
            </button>
            <button
              onClick={handleNavigateWithReplace}
              className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              Navigate to Contacts (Replace)
            </button>
            <button
              onClick={() => goToHousingDetail(123)}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
            >
              Go to Housing Detail
            </button>
          </div>
        </div>

        {/* Link examples */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">App Router Link Examples:</h3>
          <div className="space-x-4">
            <HomeLink className="text-blue-600 hover:text-blue-800 underline">
              Home (Typed Link)
            </HomeLink>
            <AdminLink className="text-green-600 hover:text-green-800 underline">
              Admin (Typed Link)
            </AdminLink>
            <ContactsLink className="text-purple-600 hover:text-purple-800 underline">
              Contacts (Typed Link)
            </ContactsLink>
            <HousingDetailLink
              id={456}
              className="text-red-600 hover:text-red-800 underline"
            >
              Housing Detail (Dynamic)
            </HousingDetailLink>
          </div>
        </div>

        {/* Route status */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Route Status:</h3>
          <div className="space-y-2">
            <p>
              <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
                isCurrentRoute(ROUTES.HOME) ? 'bg-green-500' : 'bg-gray-300'
              }`}></span>
              Home Route {isCurrentRoute(ROUTES.HOME) ? '(Active)' : '(Inactive)'}
            </p>
            <p>
              <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
                isCurrentRoute(ROUTES.ADMIN) ? 'bg-green-500' : 'bg-gray-300'
              }`}></span>
              Admin Route {isCurrentRoute(ROUTES.ADMIN) ? '(Active)' : '(Inactive)'}
            </p>
            <p>
              <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
                isCurrentRoute(ROUTES.CONTACTS) ? 'bg-green-500' : 'bg-gray-300'
              }`}></span>
              Contacts Route {isCurrentRoute(ROUTES.CONTACTS) ? '(Active)' : '(Inactive)'}
            </p>
          </div>
        </div>

        {/* Available routes */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Available App Router Routes:</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
            {Object.entries(ROUTES).map(([key, path]) => (
              <div key={key} className="p-2 bg-gray-50 rounded">
                <strong>{key}:</strong> {path}
              </div>
            ))}
          </div>
        </div>

        {/* App Router Benefits */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-3">Why App Router is Better:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-green-600 mb-2">Performance</h4>
              <ul className="text-sm space-y-1">
                <li>• Server Components reduce bundle size</li>
                <li>• Automatic code splitting</li>
                <li>• Streaming for faster page loads</li>
                <li>• Better caching strategies</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-blue-600 mb-2">Developer Experience</h4>
              <ul className="text-sm space-y-1">
                <li>• Co-located layouts and loading states</li>
                <li>• Built-in error boundaries</li>
                <li>• Type-safe navigation</li>
                <li>• Better SEO with metadata API</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
