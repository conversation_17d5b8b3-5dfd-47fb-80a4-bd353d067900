'use client'

import React from 'react'
import { useAppNavigation } from '@/hooks/useAppNavigation'
import { AppLink, HousingDetailLink } from '@/components/navigation/AppLink'

export const HomePageContent: React.FC = () => {
  const { navigateTo, isCurrentRoute } = useAppNavigation()

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <h1 className="text-4xl font-bold text-center mb-8">
          Welcome to Soodam
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Find Housing</h2>
            <p className="text-gray-600 mb-4">
              Browse through thousands of housing properties
            </p>
            <AppLink 
              href="/housing" 
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            >
              Browse Housing
            </AppLink>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Estate Consultants</h2>
            <p className="text-gray-600 mb-4">
              Connect with professional estate consultants
            </p>
            <AppLink 
              href="/estate-consultant" 
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
            >
              Find Consultants
            </AppLink>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Latest News</h2>
            <p className="text-gray-600 mb-4">
              Stay updated with real estate news
            </p>
            <AppLink 
              href="/news" 
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
            >
              Read News
            </AppLink>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold mb-4">Featured Properties</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((id) => (
              <div key={id} className="border rounded-lg p-4">
                <div className="bg-gray-200 h-32 rounded mb-3"></div>
                <h3 className="font-semibold mb-2">Property {id}</h3>
                <p className="text-gray-600 text-sm mb-3">
                  Beautiful property in great location
                </p>
                <HousingDetailLink 
                  id={id}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  View Details →
                </HousingDetailLink>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
