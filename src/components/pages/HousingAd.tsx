'use client'

import React from 'react'
import { HousingDetailLink } from '@/components/navigation/AppLink'
import { useAppNavigation } from '@/hooks/useAppNavigation'

export const HousingAdContent: React.FC = () => {
  const { navigateTo } = useAppNavigation()

  const housingAds = [
    {
      id: 1,
      title: 'Modern Apartment in Tehran',
      price: '2,500,000,000',
      location: 'Tehran, Elahieh',
      bedrooms: 3,
      bathrooms: 2,
      area: 120,
      type: 'Apartment',
    },
    {
      id: 2,
      title: 'Luxury Villa with Garden',
      price: '8,000,000,000',
      location: 'Tehran, Farmanieh',
      bedrooms: 5,
      bathrooms: 4,
      area: 350,
      type: 'Villa',
    },
    {
      id: 3,
      title: 'Commercial Office Space',
      price: '1,200,000,000',
      location: 'Tehran, Vanak',
      bedrooms: 0,
      bathrooms: 2,
      area: 80,
      type: 'Commercial',
    },
    {
      id: 4,
      title: 'Cozy Studio Apartment',
      price: '900,000,000',
      location: 'Tehran, Tajrish',
      bedrooms: 1,
      bathrooms: 1,
      area: 45,
      type: 'Studio',
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Housing Advertisements</h1>
          <button
            onClick={() => navigateTo('/housing/ad/new')}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            Post New Ad
          </button>
        </div>
        
        <div className="mb-6">
          <div className="flex flex-wrap gap-4">
            <select className="px-4 py-2 border border-gray-300 rounded-md">
              <option>All Types</option>
              <option>Apartment</option>
              <option>Villa</option>
              <option>Commercial</option>
              <option>Studio</option>
            </select>
            <select className="px-4 py-2 border border-gray-300 rounded-md">
              <option>All Locations</option>
              <option>Tehran</option>
              <option>Isfahan</option>
              <option>Mashhad</option>
              <option>Shiraz</option>
            </select>
            <select className="px-4 py-2 border border-gray-300 rounded-md">
              <option>Price Range</option>
              <option>Under 1B</option>
              <option>1B - 3B</option>
              <option>3B - 5B</option>
              <option>Over 5B</option>
            </select>
            <input
              type="text"
              placeholder="Search..."
              className="px-4 py-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {housingAds.map((ad) => (
            <div key={ad.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500">Property Image</span>
              </div>
              
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-semibold">{ad.title}</h3>
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                    {ad.type}
                  </span>
                </div>
                
                <p className="text-gray-600 mb-2">{ad.location}</p>
                
                <div className="text-xl font-bold text-green-600 mb-3">
                  {ad.price} تومان
                </div>
                
                <div className="grid grid-cols-3 gap-2 text-sm text-gray-600 mb-4">
                  <div className="text-center">
                    <div className="font-semibold">{ad.bedrooms}</div>
                    <div>Bedrooms</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">{ad.bathrooms}</div>
                    <div>Bathrooms</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">{ad.area}m²</div>
                    <div>Area</div>
                  </div>
                </div>
                
                <HousingDetailLink
                  id={ad.id}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors text-center block"
                >
                  View Details
                </HousingDetailLink>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-8 flex justify-center">
          <div className="flex space-x-2">
            <button className="px-3 py-2 border border-gray-300 rounded hover:bg-gray-50">
              Previous
            </button>
            <button className="px-3 py-2 bg-blue-600 text-white rounded">1</button>
            <button className="px-3 py-2 border border-gray-300 rounded hover:bg-gray-50">2</button>
            <button className="px-3 py-2 border border-gray-300 rounded hover:bg-gray-50">3</button>
            <button className="px-3 py-2 border border-gray-300 rounded hover:bg-gray-50">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
