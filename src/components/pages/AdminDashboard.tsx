'use client'

import React from 'react'
import { useAppNavigation } from '@/hooks/useAppNavigation'
import { AppLink } from '@/components/navigation/AppLink'

export const AdminDashboardContent: React.FC = () => {
  const { isCurrentRoute } = useAppNavigation()

  const adminMenuItems = [
    { href: '/admin/messages', label: 'Messages', icon: '💬' },
    { href: '/admin/search', label: 'Search', icon: '🔍' },
    { href: '/admin/settings', label: 'Settings', icon: '⚙️' },
    { href: '/admin/advertisements', label: 'Advertisements', icon: '📢' },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {adminMenuItems.map((item) => (
            <AppLink
              key={item.href}
              href={item.href}
              className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
              activeClassName="ring-2 ring-blue-500"
            >
              <div className="text-center">
                <div className="text-3xl mb-3">{item.icon}</div>
                <h3 className="text-lg font-semibold">{item.label}</h3>
              </div>
            </AppLink>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between py-2 border-b">
                <span>New user registration</span>
                <span className="text-sm text-gray-500">2 hours ago</span>
              </div>
              <div className="flex items-center justify-between py-2 border-b">
                <span>Property listing updated</span>
                <span className="text-sm text-gray-500">4 hours ago</span>
              </div>
              <div className="flex items-center justify-between py-2 border-b">
                <span>New message received</span>
                <span className="text-sm text-gray-500">6 hours ago</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Quick Stats</h2>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span>Total Users</span>
                <span className="font-semibold">1,234</span>
              </div>
              <div className="flex justify-between">
                <span>Active Listings</span>
                <span className="font-semibold">567</span>
              </div>
              <div className="flex justify-between">
                <span>Messages Today</span>
                <span className="font-semibold">89</span>
              </div>
              <div className="flex justify-between">
                <span>Revenue This Month</span>
                <span className="font-semibold">$12,345</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
