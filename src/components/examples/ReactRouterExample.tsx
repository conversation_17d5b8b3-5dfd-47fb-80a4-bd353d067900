import React from 'react'
import { useReactRouter } from '@/hooks'
import { RouterLink, RouteLink } from '@/components/router'
import { ROUTES, getHashUrl } from '@/utils'

export const ReactRouterExample: React.FC = () => {
  const { navigate, location, params, goToRoute, isCurrentRoute } = useReactRouter()

  const handleNavigateToHome = () => {
    navigate(ROUTES.HOME)
  }

  const handleNavigateToAdmin = () => {
    goToRoute('ADMIN')
  }

  const handleNavigateWithReplace = () => {
    navigate(ROUTES.CONTACTS, { replace: true })
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">React Router Hash Routing Example</h2>

      {/* Hash routing info */}
      <div className="mb-6 p-4 bg-blue-50 rounded border-l-4 border-blue-500">
        <h3 className="text-lg font-semibold mb-2">Hash Routing Info:</h3>
        <p className="text-sm text-gray-600 mb-2">
          This project uses hash-based routing. URLs will look like: <code className="bg-gray-200 px-1 rounded">/#/admin</code>
        </p>
        <p><strong>Current Hash URL:</strong> <code className="bg-gray-200 px-1 rounded">{typeof window !== 'undefined' ? window.location.href : 'N/A'}</code></p>
      </div>

      {/* Current location info */}
      <div className="mb-6 p-4 bg-gray-100 rounded">
        <h3 className="text-lg font-semibold mb-2">Current Location Info:</h3>
        <p><strong>Pathname:</strong> {location.pathname}</p>
        <p><strong>Search:</strong> {location.search}</p>
        <p><strong>Hash:</strong> {location.hash}</p>
        {Object.keys(params).length > 0 && (
          <p><strong>Params:</strong> {JSON.stringify(params)}</p>
        )}
      </div>

      {/* Navigation buttons */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Programmatic Navigation:</h3>
        <div className="space-x-2">
          <button
            onClick={handleNavigateToHome}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Navigate to Home
          </button>
          <button
            onClick={handleNavigateToAdmin}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Go to Admin
          </button>
          <button
            onClick={handleNavigateWithReplace}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            Navigate to Contacts (Replace)
          </button>
        </div>
      </div>

      {/* Link examples */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Link Examples:</h3>
        <div className="space-x-4">
          <RouterLink
            to={ROUTES.HOME}
            className="text-blue-600 hover:text-blue-800 underline"
          >
            Home (React Router Link)
          </RouterLink>
          <RouteLink
            route="ADMIN"
            className="text-green-600 hover:text-green-800 underline"
          >
            Admin (Route Link)
          </RouteLink>
          <RouterLink
            to={ROUTES.CONTACTS}
            useNextRouter={true}
            className="text-purple-600 hover:text-purple-800 underline"
          >
            Contacts (Next.js Link)
          </RouterLink>
        </div>
      </div>

      {/* Route status */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Route Status:</h3>
        <div className="space-y-2">
          <p>
            <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
              isCurrentRoute('HOME') ? 'bg-green-500' : 'bg-gray-300'
            }`}></span>
            Home Route {isCurrentRoute('HOME') ? '(Active)' : '(Inactive)'}
          </p>
          <p>
            <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
              isCurrentRoute('ADMIN') ? 'bg-green-500' : 'bg-gray-300'
            }`}></span>
            Admin Route {isCurrentRoute('ADMIN') ? '(Active)' : '(Inactive)'}
          </p>
          <p>
            <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
              isCurrentRoute('CONTACTS') ? 'bg-green-500' : 'bg-gray-300'
            }`}></span>
            Contacts Route {isCurrentRoute('CONTACTS') ? '(Active)' : '(Inactive)'}
          </p>
        </div>
      </div>

      {/* Hash URL examples */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Example Hash URLs:</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
          {Object.entries(ROUTES).slice(0, 6).map(([key, path]) => (
            <div key={key} className="p-2 bg-green-50 rounded border">
              <strong>{key}:</strong>
              <br />
              <code className="text-xs bg-gray-200 px-1 rounded">{getHashUrl(key as keyof typeof ROUTES)}</code>
            </div>
          ))}
        </div>
      </div>

      {/* Available routes */}
      <div>
        <h3 className="text-lg font-semibold mb-3">Available Routes:</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
          {Object.entries(ROUTES).map(([key, path]) => (
            <div key={key} className="p-2 bg-gray-50 rounded">
              <strong>{key}:</strong> {path}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ReactRouterExample
