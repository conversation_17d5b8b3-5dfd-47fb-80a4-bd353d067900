import React from 'react'
import { HashRouter, Routes, Route, Navigate } from 'react-router-dom'
import { useRouter } from 'next/router'
import { ROUTES } from '@/utils/routerConfig'

interface RouterProviderProps {
  children: React.ReactNode
}

// Component to sync Next.js router with React Router
const RouterSync: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const nextRouter = useRouter()
  
  // This component ensures that React Router stays in sync with Next.js routing
  // when needed for specific client-side navigation scenarios
  
  return (
    <Routes>
      {/* Home route */}
      <Route path={ROUTES.HOME} element={<div>{children}</div>} />
      
      {/* Admin routes */}
      <Route path={`${ROUTES.ADMIN}/*`} element={<div>{children}</div>} />
      
      {/* Authentication routes */}
      <Route path={`${ROUTES.AUTHENTICATION}/*`} element={<div>{children}</div>} />
      
      {/* Estate consultant routes */}
      <Route path={`${ROUTES.ESTATE_CONSULTANT}/*`} element={<div>{children}</div>} />
      
      {/* Housing routes */}
      <Route path={`${ROUTES.HOUSING}/*`} element={<div>{children}</div>} />
      
      {/* Other main routes */}
      <Route path={ROUTES.CONTACTS} element={<div>{children}</div>} />
      <Route path={ROUTES.FILTER_CONTROLS} element={<div>{children}</div>} />
      <Route path={ROUTES.LISTS} element={<div>{children}</div>} />
      <Route path={`${ROUTES.MARKETER}/*`} element={<div>{children}</div>} />
      <Route path={ROUTES.MEMBERSHIP} element={<div>{children}</div>} />
      <Route path={ROUTES.MY_PAYMENTS} element={<div>{children}</div>} />
      <Route path={ROUTES.MY_CITY} element={<div>{children}</div>} />
      <Route path={ROUTES.NEWS} element={<div>{children}</div>} />
      <Route path={`${ROUTES.REQUESTS}/*`} element={<div>{children}</div>} />
      <Route path={`${ROUTES.SOODAM}/*`} element={<div>{children}</div>} />
      <Route path={ROUTES.SUBSCRIPTION} element={<div>{children}</div>} />
      <Route path={ROUTES.VISIT_STATISTICS} element={<div>{children}</div>} />
      
      {/* Catch all route - redirect to current Next.js route */}
      <Route path="*" element={<div>{children}</div>} />
    </Routes>
  )
}

export const RouterProvider: React.FC<RouterProviderProps> = ({ children }) => {
  return (
    <HashRouter>
      <RouterSync>{children}</RouterSync>
    </HashRouter>
  )
}

export default RouterProvider
