import React from 'react'
import { <PERSON> as ReactRouterLink, LinkProps as ReactRouter<PERSON>ink<PERSON>rops } from 'react-router-dom'
import Link from 'next/link'
import { ROUTES, RoutePath } from '@/utils/routerConfig'

interface RouterLinkProps extends Omit<ReactRouterLinkProps, 'to'> {
  to: string | RoutePath
  useNextRouter?: boolean
  children: React.ReactNode
  className?: string
}

export const RouterLink: React.FC<RouterLinkProps> = ({
  to,
  useNextRouter = false,
  children,
  className,
  ...props
}) => {
  // If useNextRouter is true, use Next.js Link
  if (useNextRouter) {
    return (
      <Link href={to} className={className}>
        {children}
      </Link>
    )
  }

  // Otherwise, use React Router Link
  return (
    <ReactRouterLink to={to} className={className} {...props}>
      {children}
    </ReactRouterLink>
  )
}

// Helper component for route-specific links
interface RouteLink extends Omit<RouterLinkProps, 'to'> {
  route: keyof typeof ROUTES
}

export const RouteLink: React.FC<RouteLink> = ({ route, ...props }) => {
  const path = ROUTES[route]
  return <RouterLink to={path} {...props} />
}

export default RouterLink
