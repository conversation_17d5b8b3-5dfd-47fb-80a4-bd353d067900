import type { Metadata } from 'next'
import { Provider } from 'react-redux'
import { store } from '@/store'
import { ToastContainer } from 'react-toastify'
import { Toaster } from 'react-hot-toast'

// Import styles
import '@/styles/main.css'
import '@/styles/browser-styles.css'
import '@/styles/swiper.css'
import 'react-toastify/dist/ReactToastify.css'
import 'leaflet/dist/leaflet.css'

// Import providers and components
import { AppProviders } from './providers'

export const metadata: Metadata = {
  title: {
    default: 'Soodam - Real Estate Platform',
    template: '%s | Soodam'
  },
  description: 'Modern real estate platform for finding and managing properties',
  keywords: ['real estate', 'property', 'housing', 'soodam'],
  authors: [{ name: 'Soodam Team' }],
  creator: '<PERSON><PERSON>',
  publisher: 'Soo<PERSON>',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'),
  openGraph: {
    type: 'website',
    locale: 'fa_IR',
    url: '/',
    title: 'Soodam - Real Estate Platform',
    description: 'Modern real estate platform for finding and managing properties',
    siteName: 'Soodam',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Soodam - Real Estate Platform',
    description: 'Modern real estate platform for finding and managing properties',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fa" dir="rtl">
      <body>
        <AppProviders>
          {children}
          <ToastContainer />
          <Toaster />
        </AppProviders>
      </body>
    </html>
  )
}
