'use client'

import { Provider } from 'react-redux'
import { store } from '@/store'
import { useEffect, useState } from 'react'
import { LoadingScreen } from '@/components/ui'

// Mock service worker setup
async function enableMocking() {
  if (typeof window === 'undefined') return

  const { worker } = await import('../mocks/browser')
  if (process.env.NODE_ENV !== 'development') {
    worker.start({
      onUnhandledRequest: 'bypass',
    })
    return
  }

  return worker.start()
}

interface AppProvidersProps {
  children: React.ReactNode
}

export function AppProviders({ children }: AppProvidersProps) {
  const [isLoading, setIsLoading] = useState<boolean>(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 2000)

    const startMocking = async () => {
      await enableMocking()
    }

    startMocking()

    return () => clearTimeout(timer)
  }, [])

  if (isLoading) {
    return <LoadingScreen />
  }

  return (
    <Provider store={store}>
      {children}
    </Provider>
  )
}
