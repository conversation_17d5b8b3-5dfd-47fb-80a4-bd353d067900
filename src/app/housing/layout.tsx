import type { Metadata } from 'next'
import { ClientLayout } from '@/components/layouts'

export const metadata: Metadata = {
  title: {
    default: 'Housing',
    template: '%s | Housing | Soodam'
  },
  description: 'Browse and manage housing properties',
}

export default function HousingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ClientLayout title="Housing" canFilter>
      {children}
    </ClientLayout>
  )
}
