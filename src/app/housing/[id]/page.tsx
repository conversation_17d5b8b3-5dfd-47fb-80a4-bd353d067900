import type { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { HousingDetailContent } from '@/components/pages/HousingDetail'
// Import your housing service
// import { getHousingDetail } from '@/services/housing'

interface Props {
  params: { id: string }
  searchParams: { [key: string]: string | string[] | undefined }
}

// This would be your actual data fetching function
async function getHousingData(id: string) {
  try {
    // Replace with your actual API call
    // const housing = await getHousingDetail(id)
    // return housing
    
    // Mock data for now
    return {
      id,
      title: `Housing Property ${id}`,
      description: `Detailed description for housing property ${id}`,
      price: '500,000,000',
      location: 'Tehran, Iran',
      image: '/placeholder-housing.jpg',
    }
  } catch (error) {
    return null
  }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const housing = await getHousingData(params.id)

  if (!housing) {
    return {
      title: 'Housing Not Found',
    }
  }

  return {
    title: housing.title,
    description: housing.description,
    openGraph: {
      title: housing.title,
      description: housing.description,
      images: [
        {
          url: housing.image,
          width: 1200,
          height: 630,
          alt: housing.title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: housing.title,
      description: housing.description,
      images: [housing.image],
    },
  }
}

export default async function HousingDetailPage({ params }: Props) {
  const housing = await getHousingData(params.id)

  if (!housing) {
    notFound()
  }

  return <HousingDetailContent housing={housing} />
}

// Optional: Generate static params for popular housing IDs
export async function generateStaticParams() {
  // You could fetch popular housing IDs here
  // const popularHousings = await getPopularHousings()
  // return popularHousings.map((housing) => ({ id: housing.id }))
  
  return [] // Return empty array for now
}
