import type { Metadata } from 'next'
import { EstateConsultantContent } from '@/components/pages/EstateConsultant'
import { ClientLayout } from '@/components/layouts'

export const metadata: Metadata = {
  title: 'Estate Consultants',
  description: 'Find professional estate consultants',
  openGraph: {
    title: 'Estate Consultants | Soodam',
    description: 'Find professional estate consultants',
  },
}

export default function EstateConsultantPage() {
  return (
    <ClientLayout title="Estate Consultants" canFilter>
      <EstateConsultantContent />
    </ClientLayout>
  )
}
