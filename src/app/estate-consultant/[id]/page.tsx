import type { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { EstateConsultantDetailContent } from '@/components/pages/EstateConsultantDetail'
import { ClientLayout } from '@/components/layouts'

interface Props {
  params: Promise<{ id: string }>
}

async function getEstateConsultantData(id: string) {
  try {
    // Replace with your actual API call
    return {
      id,
      name: `Estate Consultant ${id}`,
      description: `Professional estate consultant with ID ${id}`,
      experience: '5+ years',
      location: 'Tehran, Iran',
      rating: 4.8,
      image: '/placeholder-consultant.jpg',
    }
  } catch (error) {
    return null
  }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = await params
  const consultant = await getEstateConsultantData(id)

  if (!consultant) {
    return {
      title: 'Consultant Not Found',
    }
  }

  return {
    title: consultant.name,
    description: consultant.description,
    openGraph: {
      title: consultant.name,
      description: consultant.description,
      images: [consultant.image],
    },
  }
}

export default async function EstateConsultantDetailPage({ params }: Props) {
  const { id } = await params
  const consultant = await getEstateConsultantData(id)

  if (!consultant) {
    notFound()
  }

  return (
    <ClientLayout title={consultant.name}>
      <EstateConsultantDetailContent consultant={consultant} />
    </ClientLayout>
  )
}
