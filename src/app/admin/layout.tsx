import type { Metadata } from 'next'
import { AdminProtectedLayout } from '@/components/layouts'

export const metadata: Metadata = {
  title: {
    default: 'Admin Dashboard',
    template: '%s | Admin | Soodam'
  },
  description: 'Admin dashboard for managing Soodam platform',
  robots: {
    index: false,
    follow: false,
  },
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AdminProtectedLayout>
      {children}
    </AdminProtectedLayout>
  )
}
