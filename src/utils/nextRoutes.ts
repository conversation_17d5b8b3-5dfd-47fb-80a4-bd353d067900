// Enhanced Next.js routing utilities - BETTER APPROACH
export const ROUTES = {
  HOME: '/',
  ADMIN: '/admin',
  ADMIN_AUTH: '/admin/authentication',
  ADMIN_MESSAGES: '/admin/messages',
  ADMIN_SEARCH: '/admin/search',
  ADMIN_SETTINGS: '/admin/settings',
  ADMIN_ADVERTISEMENTS: '/admin/advertisements',
  AUTHENTICATION: '/authentication',
  AUTHENTICATION_LOGIN: '/authentication/login',
  CONTACTS: '/contacts',
  ESTATE_CONSULTANT: '/estate-consultant',
  ESTATE_CONSULTANT_REGISTER: '/estate-consultant/register',
  FILTER_CONTROLS: '/filterControls',
  HOUSING: '/housing',
  HOUSING_AD: '/housing/ad',
  HOUSING_MY_ADS: '/housing/my-ads',
  HOUSING_UNAUTHORIZED: '/housing/unauthorize',
  LISTS: '/lists',
  MARKETER: '/marketer',
  MARKETER_REGISTER: '/marketer/register',
  MEMBERSHIP: '/memberShip',
  MY_PAYMENTS: '/my-payments',
  MY_CITY: '/myCity',
  NEWS: '/news',
  REQUESTS: '/requests',
  REQUESTS_NEW: '/requests/new',
  SOODAM: '/soodam',
  SOODAM_ACCOUNT: '/soodam/account',
  SOODAM_SETTING: '/soodam/setting',
  SUBSCRIPTION: '/subscription',
  VISIT_STATISTICS: '/visitStatistics',
} as const

export type RoutePath = typeof ROUTES[keyof typeof ROUTES]

// Enhanced navigation utilities for Next.js
export const createRoute = (path: RoutePath, params?: Record<string, string | number>): string => {
  let route = path
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      route = route.replace(`[${key}]`, String(value))
    })
  }
  return route
}

// Type-safe route building
export const buildRoute = {
  home: () => ROUTES.HOME,
  admin: () => ROUTES.ADMIN,
  adminAuth: () => ROUTES.ADMIN_AUTH,
  contacts: () => ROUTES.CONTACTS,
  housingDetail: (id: string | number) => `/housing/${id}`,
  estateConsultantDetail: (id: string | number) => `/estate-consultant/${id}`,
  // Add more as needed
}

// Route validation
export const isValidRoute = (path: string): path is RoutePath => {
  return Object.values(ROUTES).includes(path as RoutePath)
}

// Active route checking
export const isActiveRoute = (currentPath: string, targetRoute: RoutePath): boolean => {
  if (targetRoute === ROUTES.HOME) {
    return currentPath === ROUTES.HOME
  }
  return currentPath.startsWith(targetRoute)
}
