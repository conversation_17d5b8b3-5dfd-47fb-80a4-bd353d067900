
import { RouteObject } from 'react-router-dom'

// Define route paths as constants for better maintainability
// Note: These paths will be used with <PERSON><PERSON><PERSON><PERSON><PERSON>, so they'll appear as /#/path
export const ROUTES = {
  HOME: '/',
  ADMIN: '/admin',
  ADMIN_AUTH: '/admin/authentication',
  ADMIN_MESSAGES: '/admin/messages',
  ADMIN_SEARCH: '/admin/search',
  ADMIN_SETTINGS: '/admin/settings',
  ADMIN_ADVERTISEMENTS: '/admin/advertisements',
  AUTHENTICATION: '/authentication',
  AUTHENTICATION_LOGIN: '/authentication/login',
  CONTACTS: '/contacts',
  ESTATE_CONSULTANT: '/estate-consultant',
  ESTATE_CONSULTANT_REGISTER: '/estate-consultant/register',
  FILTER_CONTROLS: '/filterControls',
  HOUSING: '/housing',
  HOUSING_AD: '/housing/ad',
  HOUSING_MY_ADS: '/housing/my-ads',
  HOUSING_UNAUTHORIZED: '/housing/unauthorize',
  LISTS: '/lists',
  MARKETER: '/marketer',
  MARKETER_REGISTER: '/marketer/register',
  MEMBERSHIP: '/memberShip',
  MY_PAYMENTS: '/my-payments',
  MY_CITY: '/myCity',
  NEWS: '/news',
  REQUESTS: '/requests',
  REQUESTS_NEW: '/requests/new',
  SOODAM: '/soodam',
  SOODAM_ACCOUNT: '/soodam/account',
  SOODAM_SETTING: '/soodam/setting',
  SUBSCRIPTION: '/subscription',
  VISIT_STATISTICS: '/visitStatistics',
} as const

// Type for route paths
export type RoutePath = typeof ROUTES[keyof typeof ROUTES]

// Route configuration for React Router
export const routeConfig: RouteObject[] = [
  {
    path: ROUTES.HOME,
    id: 'home',
  },
  {
    path: ROUTES.ADMIN,
    id: 'admin',
    children: [
      {
        path: 'authentication/*',
        id: 'admin-auth',
      },
      {
        path: 'messages/*',
        id: 'admin-messages',
      },
      {
        path: 'search/*',
        id: 'admin-search',
      },
      {
        path: 'settings/*',
        id: 'admin-settings',
      },
      {
        path: 'advertisements/*',
        id: 'admin-advertisements',
      },
    ],
  },
  {
    path: ROUTES.AUTHENTICATION,
    id: 'authentication',
    children: [
      {
        path: 'login/*',
        id: 'auth-login',
      },
    ],
  },
  {
    path: ROUTES.CONTACTS,
    id: 'contacts',
  },
  {
    path: ROUTES.ESTATE_CONSULTANT,
    id: 'estate-consultant',
    children: [
      {
        path: 'register/*',
        id: 'estate-consultant-register',
      },
      {
        path: ':id/*',
        id: 'estate-consultant-detail',
      },
    ],
  },
  {
    path: ROUTES.FILTER_CONTROLS,
    id: 'filter-controls',
  },
  {
    path: ROUTES.HOUSING,
    id: 'housing',
    children: [
      {
        path: 'ad/*',
        id: 'housing-ad',
      },
      {
        path: 'my-ads/*',
        id: 'housing-my-ads',
      },
      {
        path: 'unauthorize',
        id: 'housing-unauthorized',
      },
      {
        path: ':id/*',
        id: 'housing-detail',
      },
    ],
  },
  {
    path: ROUTES.LISTS,
    id: 'lists',
  },
  {
    path: ROUTES.MARKETER,
    id: 'marketer',
    children: [
      {
        path: 'register/*',
        id: 'marketer-register',
      },
    ],
  },
  {
    path: ROUTES.MEMBERSHIP,
    id: 'membership',
  },
  {
    path: ROUTES.MY_PAYMENTS,
    id: 'my-payments',
  },
  {
    path: ROUTES.MY_CITY,
    id: 'my-city',
  },
  {
    path: ROUTES.NEWS,
    id: 'news',
  },
  {
    path: ROUTES.REQUESTS,
    id: 'requests',
    children: [
      {
        path: 'new/*',
        id: 'requests-new',
      },
    ],
  },
  {
    path: ROUTES.SOODAM,
    id: 'soodam',
    children: [
      {
        path: 'account/*',
        id: 'soodam-account',
      },
      {
        path: 'setting/*',
        id: 'soodam-setting',
      },
    ],
  },
  {
    path: ROUTES.SUBSCRIPTION,
    id: 'subscription',
  },
  {
    path: ROUTES.VISIT_STATISTICS,
    id: 'visit-statistics',
  },
]

// Helper function to navigate programmatically
export const getRoutePath = (route: keyof typeof ROUTES): string => {
  return ROUTES[route]
}

// Helper function to get the full hash URL (for external links or debugging)
export const getHashUrl = (route: keyof typeof ROUTES, baseUrl?: string): string => {
  const base = baseUrl || (typeof window !== 'undefined' ? window.location.origin : '')
  return `${base}/#${ROUTES[route]}`
}

// Helper function to check if current URL matches a hash route
export const isHashRoute = (route: keyof typeof ROUTES): boolean => {
  if (typeof window === 'undefined') return false
  return window.location.hash === `#${ROUTES[route]}`
}
